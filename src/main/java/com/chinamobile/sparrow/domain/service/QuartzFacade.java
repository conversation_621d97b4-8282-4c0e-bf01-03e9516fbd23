package com.chinamobile.sparrow.domain.service;

import org.quartz.*;
import org.quartz.impl.JobDetailImpl;
import org.quartz.impl.matchers.GroupMatcher;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.function.Consumer;

public class QuartzFacade {

    protected final Scheduler scheduler;
    protected final Logger logger;

    public QuartzFacade(Scheduler scheduler) {
        this.scheduler = scheduler;
        this.logger = LoggerFactory.getLogger(this.getClass());
    }

    public List<JobVO> findJobs() throws SchedulerException {
        List<JobVO> _records = new ArrayList<>();

        for (String i : scheduler.getTriggerGroupNames()) {
            GroupMatcher<TriggerKey> _matcher = GroupMatcher.groupEquals(i);
            for (TriggerKey j : scheduler.getTriggerKeys(_matcher)) {
                Trigger _trigger = scheduler.getTrigger(j);
                if (!(_trigger instanceof CronTrigger)) {
                    continue;
                }

                CronTrigger _cronTrigger = (CronTrigger) _trigger;
                JobKey _jonKey = _cronTrigger.getJobKey();
                JobDetailImpl _jobDetail = (JobDetailImpl) scheduler.getJobDetail(_jonKey);

                JobVO _record = new JobVO();
                _record.setJobName(_jobDetail.getName());
                _record.setJobGroup(_jobDetail.getGroup());
                _record.setJobDesc(_jobDetail.getDescription());
                _record.setTriggerName(j.getName());
                _record.setTriggerGroup(j.getGroup());
                _record.setTriggerCronExpression(_cronTrigger.getCronExpression());
                _record.setTriggerState(scheduler.getTriggerState(j).name());
                _records.add(_record);
            }
        }

        return _records;
    }

    public boolean updateCron(String name, String group, String cron) throws SchedulerException {
        TriggerKey _triggerKey = new TriggerKey(name, group);

        CronTrigger _cronTrigger = (CronTrigger) scheduler.getTrigger(_triggerKey);
        String _cron = _cronTrigger.getCronExpression();
        if (_cron.equalsIgnoreCase(cron)) {
            return true;
        }

        _cronTrigger = TriggerBuilder.newTrigger()
                .withIdentity(name, group)
                .withSchedule(CronScheduleBuilder.cronSchedule(cron))
                .build();
        return scheduler.rescheduleJob(_triggerKey, _cronTrigger) != null;
    }

    public void triggerJob(String name, String group) throws SchedulerException {
        jobDetail(jobKey -> {
            try {
                // 防止重复执行
                if (scheduler.getCurrentlyExecutingJobs().stream()
                        .anyMatch(j -> j.getJobDetail().getKey().equals(jobKey))) {
                    return;
                }

                scheduler.triggerJob(jobKey);
            } catch (SchedulerException e) {
                logger.error(String.format("触发任务[%s]失败", jobKey), e);
            }
        }, name, group);
    }

    public void pauseJob(String name, String group) throws SchedulerException {
        jobDetail(jobKey -> {
            try {
                scheduler.pauseJob(jobKey);
            } catch (SchedulerException e) {
                logger.error(String.format("停止任务[%s]失败", jobKey), e);
            }
        }, name, group);
    }

    public void resumeJob(String name, String group) throws SchedulerException {
        jobDetail(jobKey -> {
            try {
                scheduler.resumeJob(jobKey);
            } catch (SchedulerException e) {
                logger.error(String.format("恢复任务[%s]失败", jobKey), e);
            }

        }, name, group);
    }

    protected void jobDetail(Consumer<JobKey> consumer, String name, String group) throws SchedulerException {
        JobKey _jobKey = new JobKey(name, group);
        if (Optional.ofNullable(scheduler.getJobDetail(_jobKey)).isPresent()) {
            consumer.accept(_jobKey);
        }
    }

    public static class JobVO {

        String jobName;
        String jobGroup;
        String jobDesc;

        String triggerName;
        String triggerGroup;
        String triggerCronExpression;
        String triggerState;

        public String getJobName() {
            return jobName;
        }

        public void setJobName(String jobName) {
            this.jobName = jobName;
        }

        public String getJobGroup() {
            return jobGroup;
        }

        public void setJobGroup(String jobGroup) {
            this.jobGroup = jobGroup;
        }

        public String getJobDesc() {
            return jobDesc;
        }

        public void setJobDesc(String jobDesc) {
            this.jobDesc = jobDesc;
        }

        public String getTriggerName() {
            return triggerName;
        }

        public void setTriggerName(String triggerName) {
            this.triggerName = triggerName;
        }

        public String getTriggerGroup() {
            return triggerGroup;
        }

        public void setTriggerGroup(String triggerGroup) {
            this.triggerGroup = triggerGroup;
        }

        public String getTriggerCronExpression() {
            return triggerCronExpression;
        }

        public void setTriggerCronExpression(String triggerCronExpression) {
            this.triggerCronExpression = triggerCronExpression;
        }

        public String getTriggerState() {
            return triggerState;
        }

        public void setTriggerState(String triggerState) {
            this.triggerState = triggerState;
        }

    }

}