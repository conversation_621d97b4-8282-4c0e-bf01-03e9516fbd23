package com.chinamobile.sparrow.domain.service;

import com.chinamobile.sparrow.domain.infra.code.Result;

import java.io.InputStream;

public interface IFileService {

    Result<String> upload(String id, String path, String fileName, byte[] bytes);

    Result<InputStream> download(String path, String fileName);

    Result<byte[]> downloadPart(String path, String fileName, Long start, Long end);

    Result<Void> remove(String path, String fileName);

    Result<Long> getContentLength(String path, String fileName);

}