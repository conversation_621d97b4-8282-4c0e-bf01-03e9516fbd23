package com.chinamobile.sparrow.domain.service.wx.ma;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.bean.WxMaJscode2SessionResult;
import cn.binarywang.wx.miniapp.bean.WxMaPhoneNumberInfo;
import com.chinamobile.sparrow.domain.service.wx.ma.lang.AbstractFacade;
import com.chinamobile.sparrow.domain.service.wx.ma.lang.User;
import me.chanjar.weixin.common.error.WxErrorException;

public class AccessFacade extends AbstractFacade {

    public AccessFacade(WxMaService wxMaService) {
        super(wxMaService);
    }

    public User getUser(String code) throws WxErrorException {
        WxMaJscode2SessionResult _session = wxMaService.getUserService().getSessionInfo(code);

        User _user = new User();
        _user.unionId = _session.getUnionid();
        _user.openId = _session.getOpenid();
        return _user;
    }

    public User getUserWithDecryptedMp(String code, String encryptedData, String iv) throws WxErrorException {
        WxMaJscode2SessionResult _session = wxMaService.getUserService().getSessionInfo(code);
        WxMaPhoneNumberInfo _mp = wxMaService.getUserService().getPhoneNoInfo(_session.getSessionKey(), encryptedData, iv);

        User _user = new User();
        _user.unionId = _session.getUnionid();
        _user.openId = _session.getOpenid();
        _user.mp = _mp.getPurePhoneNumber();

        return _user;
    }

    public String getMp(String code) throws WxErrorException {
        WxMaPhoneNumberInfo _mp = wxMaService.getUserService().getNewPhoneNoInfo(code);
        return _mp.getPurePhoneNumber();
    }

}