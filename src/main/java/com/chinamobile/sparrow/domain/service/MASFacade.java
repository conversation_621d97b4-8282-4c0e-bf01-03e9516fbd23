package com.chinamobile.sparrow.domain.service;

import com.chinamobile.sparrow.domain.infra.code.ErrorCode;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.infra.log.Log;
import com.chinamobile.sparrow.domain.util.ConverterUtil;
import com.chinamobile.sparrow.domain.util.HttpUtil;
import com.chinamobile.sparrow.domain.util.ValidatorUtil;
import okhttp3.MediaType;
import okhttp3.RequestBody;
import okhttp3.Response;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.Base64Utils;
import org.springframework.util.CollectionUtils;
import org.springframework.util.DigestUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

// 云MAS短信服务
@Log
@ErrorCode(module = "104")
public class MASFacade {

    protected final String baseURL;
    protected final String ecName;
    protected final String apId;
    protected final String secretKey;
    protected final HttpUtil httpUtil;
    protected final Logger logger;

    public MASFacade(String baseURL, String ecName, String apId, String secretKey, HttpUtil httpUtil) {
        this.baseURL = baseURL;
        this.ecName = ecName;
        this.apId = apId;
        this.secretKey = secretKey;
        this.httpUtil = httpUtil;
        this.logger = LoggerFactory.getLogger(this.getClass());
    }

    public Result<String> sendSms(List<String> mps, String content, String sign) {
        mps = preprocess(mps);

        NormalSubmitRequest _data = new NormalSubmitRequest(ecName, apId, sign);
        _data.setMobiles(String.join(",", mps));
        _data.setContent(content);

        String _mac = ecName + apId + secretKey + _data.getMobiles() + content + sign + _data.getAddSerial();
        _mac = DigestUtils.md5DigestAsHex(_mac.getBytes());
        _data.setMac(_mac);

        String _json = ConverterUtil.toJson(_data);
        return sendSms(baseURL + "/sms/submit", _json);
    }

    public Result<String> sendSms(List<String> mps, String templateId, List<String> params, String sign) {
        mps = preprocess(mps);

        TemplateSubmitRequest _data = new TemplateSubmitRequest(ecName, apId, sign);
        _data.setTemplateId(templateId);
        _data.setMobiles(String.join(",", mps));

        String[] _params = (CollectionUtils.isEmpty(params) ? Collections.singletonList("") : params).toArray(new String[0]);
        _data.setParams(_params);

        String _mac = ecName + apId + secretKey + templateId + _data.getMobiles() + ConverterUtil.toJson(_params) + sign + _data.getAddSerial();
        _mac = DigestUtils.md5DigestAsHex(_mac.getBytes());
        _data.setMac(_mac);

        String _json = ConverterUtil.toJson(_data);
        return sendSms(baseURL + "/sms/tmpsubmit", _json);
    }

    /**
     * 去伪去重
     *
     * @param mps
     * @return
     */
    protected List<String> preprocess(List<String> mps) {
        if (CollectionUtils.isEmpty(mps)) {
            return new ArrayList<>();
        }

        return mps.stream()
                .filter(ValidatorUtil::isMp)
                .distinct()
                .collect(Collectors.toList());
    }

    protected Result<String> sendSms(String url, String json) {
        Result<String> _sn = new Result<>();

        RequestBody _body = RequestBody.create(Base64Utils.encodeToString(json.getBytes()), MediaType.get(org.springframework.http.MediaType.TEXT_PLAIN_VALUE));

        try (Response resp = httpUtil.request(url, null, _body)) {
            if (resp.body() == null) {
                _sn.setCode(Result.ENUM_ERROR.N, 1);
                return _sn;
            }

            SubmitResponse _response = ConverterUtil.json2Object(resp.body().string(), SubmitResponse.class);
            if (_response.success) {
                _sn.data = _response.msgGroup;
            } else {
                _sn.setCode(_response.rspcod);
                _sn.message = _response.rspcod;
            }
        } catch (Throwable e) {
            logger.error("发送短信失败", e);

            _sn.setCode(Result.SERVICE_UNKNOWN);
            _sn.message = e.getMessage();
        }

        return _sn;
    }

    public static class NormalSubmitRequest {

        String ecName;
        String apId;

        /**
         * 收信手机号码。英文逗号分隔，每批次限5000个号码，例：“13800138000,13800138001,13800138002”
         * 多对多模式设置为空双引号""
         */
        String mobiles;

        String content;
        String sign;
        String addSerial = "";
        String mac;

        public NormalSubmitRequest(String ecName, String apId, String sign) {
            this.ecName = ecName;
            this.apId = apId;
            this.sign = sign;
        }

        public NormalSubmitRequest(String ecName, String apId, String sign, String addSerial) {
            this.ecName = ecName;
            this.apId = apId;
            this.sign = sign;
            this.addSerial = addSerial;
        }

        public String getEcName() {
            return ecName;
        }

        public void setEcName(String ecName) {
            this.ecName = ecName;
        }

        public String getApId() {
            return apId;
        }

        public void setApId(String apId) {
            this.apId = apId;
        }

        public String getMobiles() {
            return mobiles;
        }

        public void setMobiles(String mobiles) {
            this.mobiles = mobiles;
        }

        public String getContent() {
            return content;
        }

        public void setContent(String content) {
            this.content = content;
        }

        public String getSign() {
            return sign;
        }

        public void setSign(String sign) {
            this.sign = sign;
        }

        public String getAddSerial() {
            return addSerial;
        }

        public void setAddSerial(String addSerial) {
            this.addSerial = addSerial;
        }

        public String getMac() {
            return mac;
        }

        public void setMac(String mac) {
            this.mac = mac;
        }

    }

    public static class TemplateSubmitRequest {

        String ecName;
        String apId;
        String templateId;

        /**
         * 收信手机号码。英文逗号分隔，每批次限5000个号码，例：“13800138000,13800138001,13800138002”
         * 多对多模式设置为空双引号""
         */
        String mobiles;

        String[] params;
        String sign;
        String addSerial = "";
        String mac;

        public TemplateSubmitRequest(String ecName, String apId, String sign) {
            this.ecName = ecName;
            this.apId = apId;
            this.sign = sign;
        }

        public TemplateSubmitRequest(String ecName, String apId, String sign, String addSerial) {
            this.ecName = ecName;
            this.apId = apId;
            this.sign = sign;
            this.addSerial = addSerial;
        }

        public String getEcName() {
            return ecName;
        }

        public void setEcName(String ecName) {
            this.ecName = ecName;
        }

        public String getApId() {
            return apId;
        }

        public void setApId(String apId) {
            this.apId = apId;
        }

        public String getTemplateId() {
            return templateId;
        }

        public void setTemplateId(String templateId) {
            this.templateId = templateId;
        }

        public String getMobiles() {
            return mobiles;
        }

        public void setMobiles(String mobiles) {
            this.mobiles = mobiles;
        }

        public String[] getParams() {
            return params;
        }

        public void setParams(String[] params) {
            this.params = params;
        }

        public String getSign() {
            return sign;
        }

        public void setSign(String sign) {
            this.sign = sign;
        }

        public String getAddSerial() {
            return addSerial;
        }

        public void setAddSerial(String addSerial) {
            this.addSerial = addSerial;
        }

        public String getMac() {
            return mac;
        }

        public void setMac(String mac) {
            this.mac = mac;
        }

    }

    public static class SubmitResponse {

        public boolean success;
        public String msgGroup;
        public String rspcod;

    }

}