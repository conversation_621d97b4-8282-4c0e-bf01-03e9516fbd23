package com.chinamobile.sparrow.domain.service.wx.cp;

import com.chinamobile.sparrow.domain.service.wx.cp.lang.AbstractFacade;
import com.chinamobile.sparrow.domain.service.wx.cp.lang.User;
import me.chanjar.weixin.common.bean.WxJsapiSignature;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.cp.api.WxCpService;
import me.chanjar.weixin.cp.bean.WxCpOauth2UserInfo;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

public class AccessFacade extends AbstractFacade {

    public AccessFacade(WxCpService wxCpService) {
        super(wxCpService);
    }

    public String getAuthorizationUrl(String redirectUri) throws UnsupportedEncodingException {
        redirectUri = URLEncoder.encode(redirectUri, StandardCharsets.UTF_8.name());
        return wxCpService.getOauth2Service().buildAuthorizationUrl(redirectUri, null);
    }

    public WxJsapiSignature getJsApiSignature(String url) throws WxErrorException {
        return wxCpService.createJsapiSignature(url);
    }

    public User getUser(String code) throws Exception {
        WxCpOauth2UserInfo _response = wxCpService.getOauth2Service().getUserInfo(code);

        User _user = new User();
        _user.id = _response.getUserId();
        _user.deviceId = _response.getDeviceId();
        return _user;
    }

}