package com.chinamobile.sparrow.domain.service;

import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.infra.log.Log;
import com.chinamobile.sparrow.domain.util.CryptoUtil;
import com.chinamobile.sparrow.domain.util.DateUtil;
import com.chinamobile.sparrow.domain.util.HttpUtil;
import com.chinamobile.sparrow.domain.util.ValidatorUtil;
import com.google.gson.reflect.TypeToken;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

// 企信通服务
@Log
public class QXTFacade {

    final String baseURL;
    final String eid;
    final String userId;
    final String password;
    final String encryptKey;
    final String port;
    final HttpUtil httpUtil;
    final Logger logger;

    public QXTFacade(String baseURL, String eid, String userId, String password, String encryptKey, String port, HttpUtil httpUtil) {
        this.baseURL = baseURL;
        this.eid = eid;
        this.userId = userId;
        this.password = password;
        this.encryptKey = encryptKey;
        this.port = port;
        this.httpUtil = httpUtil;
        this.logger = LoggerFactory.getLogger(this.getClass());
    }

    public Result<String> sendSms(String port, List<String> mps, String content) {
        Result<String> _id = new Result<>();

        try {
            Map<String, String> _data = new HashMap<>();
            _data.put("eid", CryptoUtil.encryptAes(eid, encryptKey));
            _data.put("userid", CryptoUtil.encryptAes(userId, encryptKey));
            _data.put("password", CryptoUtil.encryptAes(password, encryptKey));

            if (StringUtils.hasLength(port)) {
                _data.put("extport", CryptoUtil.encryptAes(port.substring(16), encryptKey));
            }

            mps = preprocess(mps);

            _data.put("mobile", CryptoUtil.encryptAes(String.join(",", mps), encryptKey));
            _data.put("content", CryptoUtil.encryptAes(content, encryptKey));

            Result<String> _response = httpUtil.form(baseURL + "/SmsHttpInterface/smsService/Do-sendSms.action", null, _data, new TypeToken<String>() {
            });
            if (!_response.isOK()) {
                return _id.pack(_response);
            }

            String[] _details = _response.data.split(",", 2);
            if (!_response.data.startsWith("0")) {
                _id.setCode(_details[0]);
                _id.message = _details[1];
                return _id;
            }

            _response.data = CryptoUtil.decryptAes(_details[1], encryptKey);
            _details = _response.data.split(",", 2);
            _id.data = _details[1];
        } catch (Throwable e) {
            logger.error("发送短信失败", e);

            _id.setCode(Result.UNKNOWN, new Object[]{e.getMessage()});
        }

        return _id;
    }

    public Result<List<SMS>> receive() {
        Result<List<SMS>> _sms = new Result<>();
        _sms.data = new ArrayList<>();

        try {
            Map<String, String> _data = new HashMap<>();
            _data.put("eid", CryptoUtil.encryptAes(eid, encryptKey));
            _data.put("userid", CryptoUtil.encryptAes(userId, encryptKey));
            _data.put("password", CryptoUtil.encryptAes(password, encryptKey));

            Result<String> _response = httpUtil.form(baseURL + "/SmsHttpInterface/smsService/Do-receive.action", null, _data, new TypeToken<String>() {
            });
            if (!_response.isOK()) {
                return _sms.pack(_response);
            }

            // 无上行短信
            if (!StringUtils.hasLength(_response.data)) {
                return _sms;
            }

            String[] _details = _response.data.split(",", 2);
            if (!_response.data.startsWith("0")) {
                _sms.setCode(_details[0]);
                _sms.message = _details[1];
                return _sms;
            }

            _response.data = CryptoUtil.decryptAes(_details[1], encryptKey);
            for (String i : _response.data.split("#")) {
                String[] _str = i.split(",");
                if (_str.length > 3) {
                    SMS _temp = new SMS();
                    _temp.setPort(_str[2]);
                    _temp.setMp(_str[1]);
                    _temp.setContent(_str[0]);
                    _temp.setTime(DateUtil.from(_str[3]));

                    _sms.data.add(_temp);
                }
            }
        } catch (Throwable e) {
            logger.error("接收短信失败", e);

            _sms.setCode(Result.UNKNOWN, new Object[]{e.getMessage()});
        }

        return _sms;
    }

    /**
     * 去伪去重
     *
     * @param mps
     * @return
     */
    protected List<String> preprocess(List<String> mps) {
        if (CollectionUtils.isEmpty(mps)) {
            return new ArrayList<>();
        }

        return mps.stream()
                .filter(ValidatorUtil::isMp)
                .distinct()
                .collect(Collectors.toList());
    }

    public static class SMS {

        String id;
        String port;
        String mp;
        String content;
        Date time;

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getPort() {
            return port;
        }

        public void setPort(String port) {
            this.port = port;
        }

        public String getMp() {
            return mp;
        }

        public void setMp(String mp) {
            this.mp = mp;
        }

        public String getContent() {
            return content;
        }

        public void setContent(String content) {
            this.content = content;
        }

        public Date getTime() {
            return time;
        }

        public void setTime(Date time) {
            this.time = time;
        }

    }

}