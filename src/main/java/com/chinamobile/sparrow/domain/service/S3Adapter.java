package com.chinamobile.sparrow.domain.service;

import com.amazonaws.ClientConfiguration;
import com.amazonaws.Protocol;
import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.client.builder.AwsClientBuilder;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3ClientBuilder;
import com.amazonaws.services.s3.model.CannedAccessControlList;
import com.amazonaws.services.s3.model.GetObjectRequest;
import com.amazonaws.services.s3.model.ObjectMetadata;
import com.amazonaws.services.s3.model.PutObjectRequest;
import com.chinamobile.sparrow.domain.infra.code.ErrorCode;
import com.chinamobile.sparrow.domain.infra.code.Result;
import org.apache.commons.io.FilenameUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.FileCopyUtils;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.function.Function;

// 对象存储服务
@ErrorCode(module = "991")
public class S3Adapter implements IFileService {

    protected final String endpoint;
    protected final String accessKey;
    protected final String secretKey;
    protected final int partSize;

    protected final Logger logger;

    public S3Adapter(String endpoint, String accessKey, String secretKey, int partSize) {
        this.endpoint = endpoint;
        this.accessKey = accessKey;
        this.secretKey = secretKey;
        this.partSize = partSize;

        this.logger = LoggerFactory.getLogger(this.getClass());
    }

    @Override
    public Result<String> upload(String key, String bucket, String fileName, byte[] bytes) {
        return request(client -> {
            Result<String> _url = new Result<>();

            ObjectMetadata _meta = new ObjectMetadata();
            _meta.setContentType(getMineType(FilenameUtils.getExtension(fileName)));
            _meta.setContentLength(bytes.length);

            try (InputStream stream = new ByteArrayInputStream(bytes)) {
                PutObjectRequest _request = new PutObjectRequest(bucket, key, stream, _meta).withCannedAcl(CannedAccessControlList.PublicRead);
                client.putObject(_request);

                _url.data = client.getUrl(bucket, key).toString();
            } catch (Throwable e) {
                logger.error("上传文件失败", e);

                _url.setCode(Result.ENUM_ERROR.F, 1, new Object[]{e.getMessage()});
            }

            return _url;
        });
    }

    @Override
    public Result<InputStream> download(String bucket, String key) {
        return request(client -> {
            Result<InputStream> _input = new Result<>();
            _input.data = client.getObject(new GetObjectRequest(bucket, key)).getObjectContent();
            return _input;
        });
    }

    @Override
    public Result<byte[]> downloadPart(String bucket, String key, Long start, Long end) {
        return request(client -> {
            Result<byte[]> _input = new Result<>();

            long _start = start == null ? 0L : start;
            long _end = end == null ? _start + partSize - 1 : end;

            long _size = client.getObject(bucket, key).getObjectMetadata().getContentLength();
            _end = Long.min(_end, _size - 1);

            GetObjectRequest _request = new GetObjectRequest(bucket, key).withRange(_start, _end);

            try {
                _input.data = FileCopyUtils.copyToByteArray(client.getObject(_request).getObjectContent());
            } catch (IOException e) {
                logger.error("读取文件失败", e);

                _input.setCode(Result.ENUM_ERROR.F, 2);
            }

            return _input;
        });
    }

    @Override
    public Result<Void> remove(String bucket, String key) {
        return request(client -> {
            client.deleteObject(bucket, key);

            return new Result<>();
        });
    }

    @Override
    public Result<Long> getContentLength(String bucket, String key) {
        return request(client -> {
            Result<Long> _length = new Result<>();
            _length.data = client.getObject(bucket, key).getObjectMetadata().getContentLength();
            return _length;
        });
    }

    protected <T> Result<T> request(Function<AmazonS3, Result<T>> function) {
        try {
            BasicAWSCredentials _credentials = new BasicAWSCredentials(accessKey, secretKey);

            ClientConfiguration _config = new ClientConfiguration();
            _config.setSignerOverride("S3SignerType"); // 设置凭证验证方式
            _config.setProtocol(Protocol.HTTP); // 设置协议

            AmazonS3 _client = AmazonS3ClientBuilder.standard()
                    .withCredentials(new AWSStaticCredentialsProvider(_credentials))
                    .withClientConfiguration(_config)
                    .withEndpointConfiguration(new AwsClientBuilder.EndpointConfiguration(endpoint, ""))
                    .withPathStyleAccessEnabled(true)
                    .build();

            return function.apply(_client);
        } catch (Throwable e) {
            logger.error("建立连接失败", e);

            Result<T> _result = new Result<>();
            _result.setCode(Result.ENUM_ERROR.F, 999, new Object[]{e.getMessage()});
            return _result;
        }
    }

    protected String getMineType(String extension) {
        switch (extension.toLowerCase()) {
            case "jpg":
            case "jpeg":
                return "image/jpeg";
            case "bmp":
                return "image/bmp";
            case "gif":
                return "image/gif";
            case "txt":
                return "text/plain";
            case "pdf":
                return "application/pdf";
            case "xls":
            case "xlsx":
                return "application/vnd.ms-excel";
            case "doc":
            case "docx":
                return "application/msword";
            case "ppt":
            case "pptx":
                return "application/vnd.ms-powerpoint";
            case "htm":
            case "html":
                return "text/html;charset=gb2312";
            default:
                return "application/octet-stream";
        }
    }

}