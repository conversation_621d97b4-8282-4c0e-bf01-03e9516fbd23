package com.chinamobile.sparrow.domain.service.cmpassport;

import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.util.CryptoUtil;
import com.chinamobile.sparrow.domain.util.DateUtil;
import com.chinamobile.sparrow.domain.util.HttpUtil;
import com.google.gson.JsonObject;
import com.google.gson.reflect.TypeToken;
import org.springframework.util.DigestUtils;
import org.springframework.util.StringUtils;

import java.nio.charset.StandardCharsets;
import java.security.*;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.PKCS8EncodedKeySpec;
import java.util.Date;
import java.util.UUID;

// 移动认证服务
public class Facade {

    protected final String CODE_OK = "103000";

    protected final String baseURL;
    protected final String appId;
    protected final String appKey;
    protected final String privateKey;
    protected final HttpUtil httpUtil;

    public Facade(String baseURL, String appId, String appKey, String privateKey, HttpUtil httpUtil) {
        this.baseURL = baseURL;
        this.appId = appId;
        this.appKey = appKey;
        this.privateKey = privateKey;
        this.httpUtil = httpUtil;
    }

    public String sign(String str) throws NoSuchAlgorithmException, InvalidKeySpecException, InvalidKeyException, SignatureException {
        byte[] _key = Base64Ext.decode(privateKey, Base64Ext.NO_WRAP);
        PKCS8EncodedKeySpec _keySpec = new PKCS8EncodedKeySpec(_key);
        PrivateKey _privateKey = KeyFactory.getInstance("RSA").generatePrivate(_keySpec);

        Signature _signature = Signature.getInstance("MD5withRSA");
        _signature.initSign(_privateKey);
        _signature.update(str.getBytes(StandardCharsets.UTF_8));

        return new String(Base64Ext.encode(_signature.sign(), Base64Ext.NO_WRAP));
    }

    public Result<String> login(String token, String userInformation, String ip) throws Exception {
        Result<String> _cipher = new Result<>();

        JsonObject _data = createRequestData(appId, appKey, token, userInformation, ip);

        Result<UniToken> _token = httpUtil.request(baseURL + "/uniapi/uniTokenValidate", null, _data, new TypeToken<UniToken>() {
        });
        if (!_token.isOK()) {
            return _cipher.pack(_token);
        }

        if (!CODE_OK.equals(_token.data.getHeader().getResultcode())) {
            _cipher.setCode(_token.data.getHeader().getResultcode());
            _cipher.message = _token.data.getHeader().getResultdesc();
            return _cipher;
        }

        String _mp = _token.data.getBody().getMsisdn();
        byte[] _key = MD5STo16Byte.encrypt2MD5toByte16(appKey);
        _cipher.data = CryptoUtil.decryptAes(_mp, new String(_key));
        return _cipher;
    }

    protected JsonObject createRequestData(String appId, String appKey, String token, String userInformation, String ip) {
        String _appType = "2";
        String _idType = "1";
        String _msgId = UUID.randomUUID().toString().replace("-", "");
        String _timestamp = DateUtil.toString(new Date(), "yyyyMMddHHmmssSSS");
        String _version = "1.2";

        String _str = _appType +
                appId +
                _idType +
                appKey +
                _msgId +
                _timestamp +
                token +
                _version;
        String _sign = DigestUtils.md5DigestAsHex(_str.getBytes());

        JsonObject _header = new JsonObject();
        _header.addProperty("apptype", _appType);
        _header.addProperty("id", appId);
        _header.addProperty("idtype", _idType);
        _header.addProperty("msgid", _msgId);
        _header.addProperty("systemtime", _timestamp);
        _header.addProperty("version", _version);
        _header.addProperty("sign", _sign);

        if (StringUtils.hasLength(ip)) {
            _header.addProperty("expandparams", String.format("UID=300|userip=%s", ip));
        }

        JsonObject _body = new JsonObject();
        _body.addProperty("token", token);
        _body.addProperty("userInformation", userInformation);

        JsonObject _json = new JsonObject();
        _json.add("header", _header);
        _json.add("body", _body);

        return _json;
    }

    public static class UniToken {

        Header header;
        Body body;

        public Header getHeader() {
            return header;
        }

        public void setHeader(Header header) {
            this.header = header;
        }

        public Body getBody() {
            return body;
        }

        public void setBody(Body body) {
            this.body = body;
        }

        public static class Header {

            String appId;
            String version;
            String inresponseto;
            String systemtime;
            String resultcode;
            String resultdesc;

            public String getAppId() {
                return appId;
            }

            public void setAppId(String appId) {
                this.appId = appId;
            }

            public String getVersion() {
                return version;
            }

            public void setVersion(String version) {
                this.version = version;
            }

            public String getInresponseto() {
                return inresponseto;
            }

            public void setInresponseto(String inresponseto) {
                this.inresponseto = inresponseto;
            }

            public String getSystemtime() {
                return systemtime;
            }

            public void setSystemtime(String systemtime) {
                this.systemtime = systemtime;
            }

            public String getResultcode() {
                return resultcode;
            }

            public void setResultcode(String resultcode) {
                this.resultcode = resultcode;
            }

            public String getResultdesc() {
                return resultdesc;
            }

            public void setResultdesc(String resultdesc) {
                this.resultdesc = resultdesc;
            }

        }

        public static class Body {

            String usessionid;
            String msisdnmask;
            String passid;
            String exresparams;
            String msisdn;

            public String getUsessionid() {
                return usessionid;
            }

            public void setUsessionid(String usessionid) {
                this.usessionid = usessionid;
            }

            public String getMsisdnmask() {
                return msisdnmask;
            }

            public void setMsisdnmask(String msisdnmask) {
                this.msisdnmask = msisdnmask;
            }

            public String getPassid() {
                return passid;
            }

            public void setPassid(String passid) {
                this.passid = passid;
            }

            public String getExresparams() {
                return exresparams;
            }

            public void setExresparams(String exresparams) {
                this.exresparams = exresparams;
            }

            public String getMsisdn() {
                return msisdn;
            }

            public void setMsisdn(String msisdn) {
                this.msisdn = msisdn;
            }

        }

    }

}