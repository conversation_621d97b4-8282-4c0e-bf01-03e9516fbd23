package com.chinamobile.sparrow.domain.service;

import com.chinamobile.sparrow.domain.infra.code.ErrorCode;
import com.chinamobile.sparrow.domain.infra.code.Result;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.FilenameUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.FileCopyUtils;

import java.io.*;

// 文件系统存储服务
@ErrorCode(module = "005")
public class FileSystemService implements IFileService {

    protected final int partSize;
    protected final Logger logger;

    public FileSystemService(int partSize) {
        this.partSize = partSize;
        this.logger = LoggerFactory.getLogger(this.getClass());
    }

    @Override
    public Result<String> upload(String id, String path, String fileName, byte[] bytes) {
        Result<String> _path = new Result<>();

        File _dir = new File(path);
        if (!_dir.exists()) {
            _dir.mkdirs();
        }

        String _path2 = path + File.separator + id + "." + FilenameUtils.getExtension(fileName);
        try {
            FileCopyUtils.copy(bytes, new File(_path2));
            _path.data = _path2;
        } catch (IOException e) {
            logger.error("保存文件失败", e);

            _path.setCode(Result.ENUM_ERROR.F, 1, new Object[]{e.getMessage()});
        }

        return _path;
    }

    @Override
    public Result<InputStream> download(String path, String fileName) {
        File _file = new File(fileName);

        Result<InputStream> _stream = new Result<>();
        try {
            _stream.data = FileUtils.openInputStream(_file);
        } catch (IOException e) {
            logger.error("读取文件失败", e);

            _stream.setCode(Result.ENUM_ERROR.F, 2, new Object[]{fileName, e.getMessage()});
        }

        return _stream;
    }

    @Override
    public Result<byte[]> downloadPart(String path, String fileName, Long start, Long end) {
        Result<byte[]> _bytes = new Result<>();

        long _start = start == null ? 0L : start;
        long _end = end == null ? _start + partSize - 1 : end;

        try (RandomAccessFile file = new RandomAccessFile(fileName, "r")) {
            _end = Long.min(_end, file.length() - 1);
            long _size = _end - _start + 1;
            _bytes.data = new byte[(int) _size];

            file.seek(start);
            file.read(_bytes.data);
        } catch (FileNotFoundException e) {
            logger.error("文件不存在", e);

            _bytes.setCode(Result.ENUM_ERROR.B, 3, new Object[]{fileName});
        } catch (IOException e) {
            logger.error("读取文件失败", e);

            _bytes.setCode(Result.ENUM_ERROR.B, 2, new Object[]{fileName, e.getMessage()});
        }

        return _bytes;
    }


    @Override
    public Result<Void> remove(String path, String fileName) {
        new File(fileName).delete();

        return new Result<>();
    }

    @Override
    public Result<Long> getContentLength(String path, String fileName) {
        Result<Long> _length = new Result<>();

        File _file = new File(fileName);
        if (_file.exists()) {
            _length.data = _file.length();
        } else {
            _length.setCode(Result.ENUM_ERROR.B, 3, new Object[]{fileName});
        }

        return _length;
    }

}