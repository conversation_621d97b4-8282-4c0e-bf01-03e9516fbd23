package com.chinamobile.sparrow.domain.infra.sec.shiro;

import org.apache.shiro.web.servlet.SimpleCookie;

import java.util.LinkedHashMap;
import java.util.List;

public class ShiroConfigurationProperties {

    SimpleCookie sessionIdCookie;
    SimpleCookie rememberMeCookie;
    ShiroSessionConfigurationProperties session;
    List<String> filterChainDefinition;

    public SimpleCookie getSessionIdCookie() {
        return sessionIdCookie;
    }

    public void setSessionIdCookie(SimpleCookie sessionIdCookie) {
        this.sessionIdCookie = sessionIdCookie;
    }

    public SimpleCookie getRememberMeCookie() {
        return rememberMeCookie;
    }

    public void setRememberMeCookie(SimpleCookie rememberMeCookie) {
        this.rememberMeCookie = rememberMeCookie;
    }

    public ShiroSessionConfigurationProperties getSession() {
        return session;
    }

    public void setSession(ShiroSessionConfigurationProperties session) {
        this.session = session;
    }

    public List<String> getFilterChainDefinition() {
        return filterChainDefinition;
    }

    public void setFilterChainDefinition(List<String> filterChainDefinition) {
        this.filterChainDefinition = filterChainDefinition;
    }

    public LinkedHashMap<String, String> getFilterChainDefinitionMap() {
        LinkedHashMap<String, String> _map = new LinkedHashMap<>();
        for (String i : filterChainDefinition) {
            String[] _array = i.split(",", 2);
            _map.put(_array[0].trim(), _array[1].trim());
        }

        return _map;
    }

    public static class ShiroSessionConfigurationProperties {

        long timeout;
        long validationInterval;

        public long getTimeout() {
            return timeout;
        }

        public void setTimeout(long timeout) {
            this.timeout = timeout;
        }

        public long getValidationInterval() {
            return validationInterval;
        }

        public void setValidationInterval(long validationInterval) {
            this.validationInterval = validationInterval;
        }

    }

}