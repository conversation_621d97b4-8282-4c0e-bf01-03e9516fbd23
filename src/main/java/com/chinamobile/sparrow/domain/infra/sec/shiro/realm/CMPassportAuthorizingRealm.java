package com.chinamobile.sparrow.domain.infra.sec.shiro.realm;

import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.infra.sec.shiro.token.CMPassportToken;
import com.chinamobile.sparrow.domain.model.sys.User;
import com.chinamobile.sparrow.domain.repository.sec.PermissionRepository;
import com.chinamobile.sparrow.domain.repository.sec.RoleRepository;
import com.chinamobile.sparrow.domain.repository.sys.UserRepository;
import com.chinamobile.sparrow.domain.service.cmpassport.Facade;
import org.apache.shiro.authc.AuthenticationInfo;
import org.apache.shiro.authc.AuthenticationToken;
import org.apache.shiro.authc.SimpleAuthenticationInfo;
import org.apache.shiro.authz.AuthorizationException;

public class CMPassportAuthorizingRealm extends AuthorizationRealm {

    public static final String REALM_TYPE = "CMPASSPORT";

    protected final Facade cmPassportFacade;

    public CMPassportAuthorizingRealm(Integer maxAttempts, UserRepository userRepository, RoleRepository roleRepository, PermissionRepository permissionRepository, Facade cmPassportFacade) {
        super(maxAttempts, userRepository, roleRepository, permissionRepository, ENUM_PRINCIPAL_FIELD.MP);
        this.cmPassportFacade = cmPassportFacade;
    }

    @Override
    public boolean supports(AuthenticationToken token) {
        return token instanceof CMPassportToken;
    }

    @Override
    protected String getPrincipal(AuthenticationToken token) {
        CMPassportToken _token = (CMPassportToken) token;
        String _principal = _token.getPrincipal().toString();
        String _password = new String((char[]) _token.getCredentials());

        try {
            Result<String> _mp = cmPassportFacade.login(_principal, _password, _token.getHost());
            return _mp.isOK() ? _mp.data : null;
        } catch (Throwable e) {
            logger.error("移动认证登录失败", e);

            throw new AuthorizationException(e);
        }
    }

    @Override
    protected AuthenticationInfo getAuthenticationInfo(AuthenticationToken token, User user) {
        return new SimpleAuthenticationInfo(user, token.getCredentials(), this.getClass().toString());
    }

}