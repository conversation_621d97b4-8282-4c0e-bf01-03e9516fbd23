package com.chinamobile.sparrow.domain.infra.sec.shiro;

import com.chinamobile.sparrow.domain.model.sys.User;
import com.chinamobile.sparrow.domain.repository.sys.UserRepository;
import org.apache.shiro.session.Session;
import org.apache.shiro.session.SessionListenerAdapter;
import org.apache.shiro.subject.SimplePrincipalCollection;
import org.apache.shiro.subject.support.DefaultSubjectContext;

public class DefaultSessionListener extends SessionListenerAdapter {

    protected final UserRepository userRepository;

    public DefaultSessionListener(UserRepository userRepository) {
        this.userRepository = userRepository;
    }

    // 会话失效时更新登录状态
    @Override
    public void onExpiration(Session session) {
        logout(session);

        super.onExpiration(session);
    }

    protected void logout(Session session) {
        Object _principal = session.getAttribute(DefaultSubjectContext.PRINCIPALS_SESSION_KEY);
        if (!(_principal instanceof SimplePrincipalCollection)) {
            return;
        }

        _principal = ((SimplePrincipalCollection) _principal).getPrimaryPrincipal();
        if (_principal instanceof User) {
            userRepository.logout(((User) _principal).getId());
        }
    }

}