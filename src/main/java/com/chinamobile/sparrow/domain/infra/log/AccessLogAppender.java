package com.chinamobile.sparrow.domain.infra.log;

import ch.qos.logback.classic.Level;
import ch.qos.logback.classic.spi.ILoggingEvent;
import ch.qos.logback.classic.spi.IThrowableProxy;
import ch.qos.logback.classic.spi.StackTraceElementProxy;
import ch.qos.logback.core.db.DBAppenderBase;
import ch.qos.logback.core.db.DBHelper;
import ch.qos.logback.core.db.dialect.SQLDialectCode;
import com.chinamobile.sparrow.domain.util.DateUtil;
import com.chinamobile.sparrow.domain.util.IdWorker;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.lang.reflect.Method;
import java.sql.*;
import java.util.Date;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

public class AccessLogAppender extends DBAppenderBase<ILoggingEvent> {

    final int ID_INDEX = 1;
    final int LEVEL_INDEX = 2;
    final int TRANSACTION_ID_INDEX = 3;
    final int STACK_INDEX = 4;
    final int METHOD_INDEX = 5;
    final int REQUEST_INDEX = 6;
    final int RESPONSE_INDEX = 7;
    final int IP_INDEX = 8;
    final int ACTOR_ID_INDEX = 9;
    final int REALM_TYPE_INDEX = 10;
    final int START_TIME_INDEX = 11;
    final int END_TIME_INDEX = 12;
    final int RESPONSE_CODE_INDEX = 13;
    final int EXCEPTION_INDEX = 13;
    final int STACK_TRACE_INDEX = 14;

    final String INFO_LOG_PREFIX = "sys_info_logs_";
    final String ERROR_LOG_PREFIX = "sys_error_logs_";
    final Set<String> tableNames = new HashSet<>();

    @Override
    protected Method getGeneratedKeysMethod() {
        return null;
    }

    @Override
    protected String getInsertSQL() {
        return null;
    }

    protected String getInsertSQL(Level level) {
        if (Level.INFO.equals(level)) {
            return "INSERT INTO " + INFO_LOG_PREFIX + DateUtil.toString(new Date(), "yyyyMM") + " (id, level, transactionId, stack, method, request, response, ip, actorId, realmType, startTime, endTime, responseCode) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?);";
        } else if (Level.ERROR.equals(level)) {
            return "INSERT INTO " + ERROR_LOG_PREFIX + DateUtil.toString(new Date(), "yyyyMM") + " (id, level, transactionId, stack, method, request, response, ip, actorId, realmType, startTime, endTime, exception, stackTrace) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        }

        return null;
    }

    @Override
    public void append(ILoggingEvent eventObject) {
        Connection connection = null;
        PreparedStatement insertStatement = null;

        try {
            connection = connectionSource.getConnection();
            connection.setAutoCommit(false);
            if (cnxSupportsGetGeneratedKeys) {
                String EVENT_ID_COL_NAME = "EVENT_ID";
                if (connectionSource.getSQLDialectCode() == SQLDialectCode.POSTGRES_DIALECT) {
                    EVENT_ID_COL_NAME = EVENT_ID_COL_NAME.toLowerCase();
                }

                insertStatement = connection.prepareStatement(getInsertSQL(eventObject.getLevel()), new String[]{EVENT_ID_COL_NAME});
            } else {
                insertStatement = connection.prepareStatement(getInsertSQL(eventObject.getLevel()));
            }

            long eventId;
            synchronized (this) {
                table(connection, eventObject.getLevel());

                subAppend(eventObject, connection, insertStatement);
                eventId = selectEventId(insertStatement, connection);
            }

            secondarySubAppend(eventObject, connection, eventId);
            connection.commit();
        } catch (Throwable e) {
            addError("problem appending event", e);
        } finally {
            DBHelper.closeStatement(insertStatement);
            DBHelper.closeConnection(connection);
        }
    }

    @Override
    protected void subAppend(ILoggingEvent iLoggingEvent, Connection connection, PreparedStatement preparedStatement) throws Throwable {
        bindLoggingEventWithInsertStatement(iLoggingEvent, preparedStatement);

        int _count = preparedStatement.executeUpdate();
        if (_count != 1) {
            addWarn("Failed to insert loggingEvent");
        }
    }

    @Override
    protected void secondarySubAppend(ILoggingEvent iLoggingEvent, Connection connection, long l) throws Throwable {
    }

    protected void bindLoggingEventWithInsertStatement(ILoggingEvent iLoggingEvent, PreparedStatement preparedStatement) throws SQLException {
        Map<String, String> _mdc = iLoggingEvent.getMDCPropertyMap();

        String _transactionId = _mdc.getOrDefault("transactionId", null);
        String _stack = _mdc.getOrDefault("stack", null);
        String _request = _mdc.getOrDefault("request", null);
        String _response = _mdc.getOrDefault("response", null);
        String _ip = _mdc.getOrDefault("ip", null);
        String _actorId = _mdc.getOrDefault("actorId", null);
        String _realmType = _mdc.getOrDefault("realmType", null);
        String _startTime = _mdc.getOrDefault("startTime", null);
        String _endTime = _mdc.getOrDefault("endTime", null);

        preparedStatement.setLong(ID_INDEX, IdWorker.getInstance().nextId());
        preparedStatement.setString(LEVEL_INDEX, iLoggingEvent.getLevel().toString());
        preparedStatement.setString(TRANSACTION_ID_INDEX, _transactionId);
        preparedStatement.setString(STACK_INDEX, _stack);
        preparedStatement.setString(METHOD_INDEX, iLoggingEvent.getMessage());
        preparedStatement.setString(REQUEST_INDEX, _request);
        preparedStatement.setString(RESPONSE_INDEX, _response);
        preparedStatement.setString(IP_INDEX, _ip);
        preparedStatement.setString(ACTOR_ID_INDEX, _actorId);
        preparedStatement.setString(REALM_TYPE_INDEX, _realmType);
        preparedStatement.setString(START_TIME_INDEX, _startTime);
        preparedStatement.setString(END_TIME_INDEX, _endTime);

        if (Level.INFO.equals(iLoggingEvent.getLevel())) {
            String _responseCode = _mdc.getOrDefault("responseCode", null);
            preparedStatement.setString(RESPONSE_CODE_INDEX, _responseCode);
        } else if (Level.ERROR.equals(iLoggingEvent.getLevel())) {
            IThrowableProxy _throwable = iLoggingEvent.getThrowableProxy();

            preparedStatement.setString(EXCEPTION_INDEX, _throwable.getMessage());

            StringBuilder _str = new StringBuilder();
            for (StackTraceElementProxy i : _throwable.getStackTraceElementProxyArray()) {
                _str.append(i.getSTEAsString()).append("\n");
            }
            preparedStatement.setString(STACK_TRACE_INDEX, _str.toString());
        }
    }

    void table(Connection connection, Level level) throws SQLException {
        if (CollectionUtils.isEmpty(tableNames)) {
            ResultSet _tables = connection.getMetaData().getTables(connection.getCatalog(), null, null, new String[]{"TABLE"});
            while (_tables.next()) {
                String _tableName = _tables.getString("TABLE_NAME");
                if (_tableName.startsWith(INFO_LOG_PREFIX) || _tableName.startsWith(ERROR_LOG_PREFIX)) {
                    tableNames.add(_tableName);
                }
            }
        }

        String _suffix = DateUtil.toString(new Date(), "yyyyMM"), _tableName = null;
        if (level == Level.INFO) {
            _tableName = INFO_LOG_PREFIX + _suffix;
        } else if (level == Level.ERROR) {
            _tableName = ERROR_LOG_PREFIX + _suffix;
        }
        if (!StringUtils.hasLength(_tableName)) {
            return;
        }

        if (!tableNames.contains(_tableName)) {
            try (Statement statement = connection.createStatement()) {
                if (Level.INFO == level) {
                    // 创建表
                    String _sql = "CREATE TABLE " + _tableName + " (id BIGINT NOT NULL PRIMARY KEY, level VARCHAR(10), transactionId VARCHAR(32), stack VARCHAR(255), method TEXT, request TEXT, response TEXT, ip VARCHAR(128), actorId VARCHAR(36), realmType VARCHAR(32), startTime DATETIME(3), endTime DATETIME(3), responseCode VARCHAR(255))";
                    statement.execute(_sql);

                    // 创建索引
                    _sql = "CREATE INDEX index_response_code ON " + _tableName + " (responseCode)";
                    statement.execute(_sql);
                    _sql = "CREATE INDEX index_actor_id ON " + _tableName + " (actorId)";
                    statement.execute(_sql);
                    _sql = "CREATE INDEX index_start_time ON " + _tableName + " (startTime)";
                    statement.execute(_sql);
                    _sql = "CREATE INDEX index_actor_id_start_time ON " + _tableName + " (actorId, startTime)";
                    statement.execute(_sql);
                } else if (Level.ERROR == level) {
                    // 创建表
                    String _sql = "CREATE TABLE " + _tableName + " (id BIGINT NOT NULL PRIMARY KEY, level VARCHAR(10), transactionId VARCHAR(32), stack VARCHAR(255), method TEXT, request TEXT, response TEXT, ip VARCHAR(128), actorId VARCHAR(18), realmType VARCHAR(32), startTime DATETIME(3), endTime DATETIME(3), exception TEXT, stackTrace TEXT)";
                    statement.execute(_sql);

                    // 创建索引
                    _sql = "CREATE INDEX index_actor_id ON " + _tableName + " (actorId)";
                    statement.execute(_sql);
                    _sql = "CREATE INDEX index_start_time ON " + _tableName + " (startTime)";
                    statement.execute(_sql);
                }
            }

            tableNames.add(_tableName);
        }
    }

}