package com.chinamobile.sparrow.domain.infra.sec.shiro.realm;

import com.chinamobile.sparrow.domain.infra.sec.shiro.token.WxCpToken;
import com.chinamobile.sparrow.domain.model.sys.User;
import com.chinamobile.sparrow.domain.repository.sec.PermissionRepository;
import com.chinamobile.sparrow.domain.repository.sec.RoleRepository;
import com.chinamobile.sparrow.domain.repository.sys.UserRepository;
import com.chinamobile.sparrow.domain.service.wx.cp.AccessFacade;
import org.apache.shiro.authc.*;

public class WxCpAuthorizingRealm extends AuthorizationRealm {

    public static final String REALM_TYPE = "WXCP";

    protected final AccessFacade accessFacade;

    public WxCpAuthorizingRealm(Integer maxAttempts, UserRepository userRepository, RoleRepository roleRepository, PermissionRepository permissionRepository, AccessFacade accessFacade) {
        super(maxAttempts, userRepository, roleRepository, permissionRepository, null);
        this.accessFacade = accessFacade;
    }

    @Override
    public boolean supports(AuthenticationToken token) {
        return token instanceof WxCpToken;
    }

    @Override
    protected String getPrincipal(AuthenticationToken token) {
        String _code = token.getPrincipal().toString();

        com.chinamobile.sparrow.domain.service.wx.cp.lang.User _user;
        try {
            _user = accessFacade.getUser(_code);
        } catch (Throwable e) {
            logger.error("企业微信授权码验证失败", e);

            throw new AuthenticationException(e);
        }

        if (_user == null) {
            throw new UnknownAccountException("企业微信授权码错误，登录失败");
        }

        return _user.id;
    }

    @Override
    protected AuthenticationInfo getAuthenticationInfo(AuthenticationToken token, User user) {
        return new SimpleAuthenticationInfo(user, WxCpToken.DEFAULT_PASSWORD, this.getClass().toString());
    }

}