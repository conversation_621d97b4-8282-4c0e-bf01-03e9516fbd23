package com.chinamobile.sparrow.domain.infra.sec.shiro;

import org.apache.shiro.authz.Authorizer;
import org.apache.shiro.authz.ModularRealmAuthorizer;
import org.apache.shiro.realm.Realm;
import org.apache.shiro.subject.PrincipalCollection;

import java.util.Iterator;

public class DefaultModularRealmAuthorizer extends ModularRealmAuthorizer {

    // 重写批量鉴权算法，减少查询次数，提高响应速度
    // 一是使用批量查询而非单个查询
    // 二是只执行一次查询（因为所有realm都使用同一个查询权限的方法）
    @Override
    public boolean[] isPermitted(PrincipalCollection principals, String... permissions) {
        this.assertRealmsConfigured();
        if (permissions != null && permissions.length > 0) {
            boolean[] _isPermitted = new boolean[permissions.length];

            Iterator<Realm> _iterator = getRealms().iterator();
            Realm _realm = _iterator.hasNext() ? _iterator.next() : null;
            return (!(_realm instanceof Authorizer)) ? _isPermitted : ((Authorizer) _realm).isPermitted(principals, permissions);
        } else {
            return new boolean[0];
        }
    }

}