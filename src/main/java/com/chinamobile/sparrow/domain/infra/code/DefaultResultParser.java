package com.chinamobile.sparrow.domain.infra.code;

import org.apache.shiro.authc.*;
import org.apache.shiro.authz.AuthorizationException;
import org.apache.shiro.authz.HostUnauthorizedException;
import org.apache.shiro.authz.UnauthenticatedException;
import org.apache.shiro.authz.UnauthorizedException;
import org.hibernate.exception.ConstraintViolationException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;

import javax.persistence.PersistenceException;
import javax.validation.ConstraintViolation;

public class DefaultResultParser {

    protected final Logger logger;

    public DefaultResultParser() {
        this.logger = LoggerFactory.getLogger(this.getClass());
    }

    public Result<Void> fromException(Throwable e, boolean unknown) {
        if (e instanceof javax.validation.ConstraintViolationException) {
            ConstraintViolation<?> _error = ((javax.validation.ConstraintViolationException) e).getConstraintViolations().stream()
                    .findFirst().orElse(null);
            if (_error != null) {
                Result<Void> _result = new Result<>();
                _result.setCode(Result.DATA_VALIDATE_ERROR, new Object[]{_error.getPropertyPath(), _error.getMessage(), _error.getInvalidValue()});
                return _result;
            }
        }

        if (e instanceof BindException) {
            FieldError _fieldError = ((BindException) e).getFieldErrors().stream()
                    .findFirst().orElse(null);
            if (_fieldError != null) {
                Result<Void> _result = new Result<>();
                _result.setCode(Result.DATA_VALIDATE_ERROR, new Object[]{_fieldError.getField(), _fieldError.getDefaultMessage(), _fieldError.getRejectedValue()});
                return _result;
            }
        }

        if (e instanceof PersistenceException) {
            Throwable _cause = e.getCause();
            if (_cause instanceof ConstraintViolationException) {
                Result<Void> _result = new Result<>();
                _result.setCode(Result.DATABASE_UNKNOWN, new Object[]{((ConstraintViolationException) _cause).getSQLException().getMessage()});
                return _result;
            }
        }

        // 帐号错误
        if (e instanceof UnknownAccountException) {
            return new Result<>("001_P_012");
        }

        // 帐号被锁定
        if (e instanceof LockedAccountException) {
            return new Result<>("001_B_013");
        }

        // 帐号已失效
        if (e instanceof DisabledAccountException) {
            return new Result<>("001_B_014");
        }

        // 重试次数
        if (e instanceof ExcessiveAttemptsException) {
            return new Result<>("001_P_015");
        }

        // 多地登录
        if (e instanceof ConcurrentAccessException) {
            return new Result<>("001_B_016");
        }

        // 账号状态不正常
        if (e instanceof AccountException) {
            return new Result<>("001_B_017");
        }

        // 凭证过期
        if (e instanceof ExpiredCredentialsException) {
            return new Result<>("001_P_018");
        }

        // 凭证错误
        if (e instanceof IncorrectCredentialsException) {
            return new Result<>("001_P_019");
        }

        // 其它身份验证错误
        if (e instanceof AuthenticationException) {
            return new Result<>("001_B_020");
        }

        // 未登录
        if (e instanceof UnauthenticatedException) {
            return new Result<>("001_P_021");
        }

        // 设备鉴权失败
        if (e instanceof HostUnauthorizedException) {
            return new Result<>("001_B_022");
        }

        // 账号鉴权失败
        if (e instanceof UnauthorizedException) {
            return new Result<>("001_B_023");
        }

        // 其它鉴权失败
        if (e instanceof AuthorizationException) {
            return new Result<>("001_B_024");
        }

        return unknown ? new Result<>(Result.UNKNOWN) : null;
    }

    public Result<Void> fromException(String name, boolean unknown) {
        try {
            if (StringUtils.hasLength(name)) {
                Object e = Class.forName(name).newInstance();
                if (e instanceof Throwable) {
                    return fromException((Throwable) e, unknown);
                }
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            logger.error(e.getMessage(), e);
        }

        return unknown ? new Result<>(Result.UNKNOWN) : null;
    }

}