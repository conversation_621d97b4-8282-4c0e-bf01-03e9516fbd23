package com.chinamobile.sparrow.domain.infra.sec.shiro;

import com.chinamobile.sparrow.domain.infra.sec.shiro.realm.ConfigUserAuthorizingRealm;
import com.chinamobile.sparrow.domain.infra.sec.shiro.token.ConfigUserToken;
import org.apache.shiro.ShiroException;
import org.apache.shiro.authc.AuthenticationException;
import org.apache.shiro.authc.AuthenticationToken;
import org.apache.shiro.subject.Subject;
import org.apache.shiro.web.filter.authc.FormAuthenticationFilter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpMethod;
import org.springframework.util.StringUtils;

import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;

public class DefaultAuthenticationFilter extends FormAuthenticationFilter {

    protected final String ERROR_ATTRIBUTE = DefaultAuthenticationFilter.class.getName() + ".ERROR";

    protected final String username;
    protected final String loginUrl;
    protected final LoginUtil loginUtil;
    protected final Logger logger;

    public DefaultAuthenticationFilter(String username, String loginUrl, LoginUtil loginUtil) {
        this.username = username;
        this.loginUrl = loginUrl;
        this.loginUtil = loginUtil;
        this.logger = LoggerFactory.getLogger(this.getClass());
    }

    @Override
    protected AuthenticationToken createToken(ServletRequest request, ServletResponse response) {
        HttpServletRequest _request = (HttpServletRequest) request;

        return ConfigUserAuthorizingRealm.REALM_TYPE.equals(loginUtil.getRealmType(_request)) ? new ConfigUserToken(username, false, request.getRemoteHost()) : null;
    }

    @Override
    protected boolean isAccessAllowed(ServletRequest request, ServletResponse response, Object mappedValue) {
        Subject _subject = getSubject(request, response);
        if (_subject.getPrincipal() != null || !isLoginRequest(request, response) && isPermissive(mappedValue)) {
            return true;
        }

        try {
            AuthenticationToken _token = createToken(request, response);
            if (_token instanceof ConfigUserToken) {
                loginUtil.login(_token);

                return true;
            }
        } catch (Throwable e) {
            logger.error("ConfigUserToken验证失败", e);

            request.setAttribute(ERROR_ATTRIBUTE, e);
        }

        return false;
    }

    // 重写重定向登录方法，修改重定向地址
    @Override
    protected void redirectToLogin(ServletRequest request, ServletResponse response) throws IOException {
        // 对于非GET的请求，验证失败则抛出异常到ErrorController处理，不能执行重定向
        HttpServletRequest _request = (HttpServletRequest) request;
        if (HttpMethod.valueOf(_request.getMethod()) != HttpMethod.GET) {
            throwException(_request);
        }

        if (ConfigUserAuthorizingRealm.REALM_TYPE.equals(loginUtil.getRealmType(_request))) {
            // 已登录但验证失败，则抛出异常到ErrorController处理，不重定向到登录页面
            throwException(_request);

            return;
        }

        // 在代理下无法读取正确的requestURL
        /* String _url = _request.getRequestURL().toString();
        String _query = _request.getQueryString();
        if (StringUtils.hasLength(_query)) {
            _url = String.format("%s?%s", _url, _query);
        }
        _url = URLEncoder.encode(_url, StandardCharsets.UTF_8.name());

        if (StringUtils.hasLength(loginUrl)) {
            String _loginUrl = loginUrl + (StringUtils.hasLength(_url) ? ("?redirect=" + _url) : "");

            // 重定向到登录页
            setLoginUrl(_loginUrl);

            super.redirectToLogin(request, response);
        } else {
            throwException(_request);
        } */

        if (StringUtils.hasLength(loginUrl)) {
            setLoginUrl(loginUrl);
            super.redirectToLogin(request, response);
        } else {
            throwException(_request);
        }
    }

    protected void throwException(HttpServletRequest request) {
        Object e = request.getAttribute(ERROR_ATTRIBUTE);
        if (e == null) {
            throw new AuthenticationException();
        } else if (e instanceof ShiroException) {
            throw (ShiroException) e;
        } else {
            throw new AuthenticationException(((Exception) e).getMessage());
        }
    }

}