package com.chinamobile.sparrow.domain.infra.sec.shiro.realm;

import com.chinamobile.sparrow.domain.infra.sec.shiro.token.SMSToken;
import com.chinamobile.sparrow.domain.model.sec.VerificationCode;
import com.chinamobile.sparrow.domain.model.sys.User;
import com.chinamobile.sparrow.domain.repository.sec.PermissionRepository;
import com.chinamobile.sparrow.domain.repository.sec.RoleRepository;
import com.chinamobile.sparrow.domain.repository.sec.VerificationCodeRepository;
import com.chinamobile.sparrow.domain.repository.sys.UserRepository;
import org.apache.shiro.authc.AuthenticationInfo;
import org.apache.shiro.authc.AuthenticationToken;
import org.apache.shiro.authc.IncorrectCredentialsException;
import org.apache.shiro.authc.SimpleAuthenticationInfo;

public class SMSAuthorizingRealm extends AuthorizationRealm {

    public static final String REALM_TYPE = "SMS";

    protected final String clientId;
    protected final VerificationCodeRepository verificationCodeRepository;

    public SMSAuthorizingRealm(String clientId, Integer maxAttempts, UserRepository userRepository, RoleRepository roleRepository, PermissionRepository permissionRepository, VerificationCodeRepository verificationCodeRepository) {
        super(maxAttempts, userRepository, roleRepository, permissionRepository, ENUM_PRINCIPAL_FIELD.MP);
        this.clientId = clientId;
        this.verificationCodeRepository = verificationCodeRepository;
    }

    @Override
    public boolean supports(AuthenticationToken token) {
        return token instanceof SMSToken;
    }

    @Override
    protected String getPrincipal(AuthenticationToken token) {
        return token.getPrincipal().toString();
    }

    @Override
    protected AuthenticationInfo getAuthenticationInfo(AuthenticationToken token, User user) {
        String _mp = token.getPrincipal().toString();
        VerificationCode _code = verificationCodeRepository.get(clientId, _mp);
        if (_code == null) {
            throw new IncorrectCredentialsException();
        }

        return new SimpleAuthenticationInfo(user, _code.getCode(), this.getClass().toString());
    }

}