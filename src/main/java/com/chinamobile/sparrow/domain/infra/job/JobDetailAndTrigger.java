package com.chinamobile.sparrow.domain.infra.job;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target(value = {ElementType.TYPE})
@Retention(value = RetentionPolicy.RUNTIME)
public @interface JobDetailAndTrigger {

    String jobName();

    String jobGroup();

    String triggerName();

    String triggerGroup();

    String triggerCron();

    boolean triggerOnStart() default true;

}