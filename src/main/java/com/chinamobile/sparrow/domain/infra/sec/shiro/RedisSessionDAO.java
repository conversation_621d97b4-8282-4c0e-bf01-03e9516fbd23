package com.chinamobile.sparrow.domain.infra.sec.shiro;

import org.apache.shiro.session.Session;
import org.apache.shiro.session.UnknownSessionException;
import org.apache.shiro.session.mgt.eis.AbstractSessionDAO;
import org.springframework.data.redis.core.RedisTemplate;

import java.io.Serializable;
import java.util.Collection;
import java.util.Objects;
import java.util.stream.Collectors;

public class RedisSessionDAO extends AbstractSessionDAO {

    protected final String keyTemplate;
    protected final RedisTemplate<String, Object> redisTemplate;

    public RedisSessionDAO(String keyTemplate, RedisTemplate<String, Object> redisTemplate) {
        this.keyTemplate = keyTemplate;
        this.redisTemplate = redisTemplate;
    }

    @Override
    protected Serializable doCreate(Session session) {
        Serializable _sessionId = super.generateSessionId(session);
        super.assignSessionId(session, _sessionId);

        saveSession(session);

        return _sessionId;
    }

    @Override
    protected Session doReadSession(Serializable sessionId) {
        return (Session) redisTemplate.opsForValue().get(key(sessionId.toString()));
    }

    @Override
    public void update(Session session) throws UnknownSessionException {
        saveSession(session);
    }

    @Override
    public void delete(Session session) {
        redisTemplate.delete(key(session.getId().toString()));
    }

    @Override
    public Collection<Session> getActiveSessions() {
        return Objects.requireNonNull(redisTemplate.keys(key("*"))).stream()
                .map(i -> (Session) redisTemplate.opsForValue().get(i))
                .collect(Collectors.toList());
    }

    protected void saveSession(Session session) {
        redisTemplate.opsForValue().set(key(session.getId().toString()), session);
    }

    protected String key(String sessionId) {
        return String.format(keyTemplate, sessionId);
    }

}