package com.chinamobile.sparrow.domain.infra.sec.shiro;

import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.infra.sec.shiro.token.SMSToken;
import com.chinamobile.sparrow.domain.model.sys.User;
import com.chinamobile.sparrow.domain.repository.sys.UserRepository;
import org.apache.shiro.authc.*;
import org.apache.shiro.subject.PrincipalCollection;

import java.util.Optional;

public class DefaultAuthenticationListener implements AuthenticationListener {

    protected final UserRepository userRepository;

    public DefaultAuthenticationListener(UserRepository userRepository) {
        this.userRepository = userRepository;
    }

    @Override
    public void onSuccess(AuthenticationToken authenticationToken, AuthenticationInfo authenticationInfo) {
        Object _principal = authenticationInfo.getPrincipals().getPrimaryPrincipal();

        if (_principal instanceof User) {
            // 更新在线状态
            userRepository.login(((User) _principal).getId());
        }
    }

    @Override
    public void onFailure(AuthenticationToken authenticationToken, AuthenticationException e) {
        Result<User> _user = null;

        if (authenticationToken instanceof SMSToken) {
            String _mp = ((SMSToken) authenticationToken).getUsername();

            _user = userRepository.getBriefByIdOrAccountOrMp(_mp, true);
        } else if (authenticationToken instanceof UsernamePasswordToken) {
            String _account = ((UsernamePasswordToken) authenticationToken).getUsername();

            _user = userRepository.getBriefByIdOrAccountOrMp(_account, true);
        }

        if (_user != null && _user.isOK()) {
            // 更新尝试登录次数
            _user.data.setLoginAttempts(Optional.ofNullable(_user.data.getLoginAttempts())
                    .map(i -> (short) (i + 1)).orElse((short) 1));
            userRepository.update(_user.data, _user.data.getId());
        }
    }

    @Override
    public void onLogout(PrincipalCollection principalCollection) {
        Object _principal = principalCollection.getPrimaryPrincipal();

        if (_principal instanceof User) {
            // 更新在线状态
            userRepository.logout(((User) _principal).getId());
        }
    }

}