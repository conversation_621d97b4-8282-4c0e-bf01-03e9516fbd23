package com.chinamobile.sparrow.domain.infra.sec.shiro;

import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.infra.sec.shiro.realm.*;
import com.chinamobile.sparrow.domain.infra.sec.shiro.token.*;
import com.chinamobile.sparrow.domain.model.sys.User;
import com.chinamobile.sparrow.domain.repository.sys.UserRepository;
import com.chinamobile.sparrow.domain.util.CryptoUtil;
import com.google.gson.JsonObject;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.authc.AuthenticationToken;
import org.apache.shiro.authz.UnauthenticatedException;
import org.apache.shiro.session.mgt.eis.SessionDAO;
import org.apache.shiro.subject.SimplePrincipalCollection;
import org.apache.shiro.subject.Subject;
import org.apache.shiro.subject.support.DefaultSubjectContext;
import org.apache.shiro.web.util.SavedRequest;
import org.apache.shiro.web.util.WebUtils;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletRequest;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.Objects;
import java.util.Optional;

public class LoginUtil {

    protected final static String CAPTCHA_SESSION_ATTRIBUTE = "CAPTCHA";

    protected final static String REALM_SESSION_ATTRIBUTE = "SHIRO_REALM";
    protected final String rsaPrivateKey;
    protected final String username;
    protected final UserRepository userRepository;
    protected final SessionDAO sessionDAO;

    public LoginUtil(String rsaPrivateKey, String username, UserRepository userRepository, SessionDAO sessionDAO) {
        this.rsaPrivateKey = rsaPrivateKey;
        this.username = username;
        this.userRepository = userRepository;
        this.sessionDAO = sessionDAO;
    }

    public User getUser() {
        Subject _subject = SecurityUtils.getSubject();
        if (_subject.getPrincipal() == null || !(_subject.getPrincipal() instanceof User)) {
            throw new UnauthenticatedException();
        }

        return (User) _subject.getPrincipal();
    }

    public String getUserId() {
        return getUser().getId();
    }

    public String getUsername() {
        return getUser().getName();
    }

    public User getUserMasked() {
        return userRepository.mask(getUser());
    }

    public String getRealmType(HttpServletRequest request) {
        if (StringUtils.hasLength(username)) {
            return ConfigUserAuthorizingRealm.REALM_TYPE;
        }

        Subject _subject = SecurityUtils.getSubject();
        if (_subject.isRemembered()) {
            return "REMEMBER_ME";
        }

        return _subject.isAuthenticated() ? Optional.ofNullable(_subject.getSession().getAttribute(REALM_SESSION_ATTRIBUTE))
                .map(Object::toString).orElse(null) : null;
    }

    public Result<String> login(HttpServletRequest request, JsonObject data, String realmType, boolean requireCaptcha, boolean decryptRsa) throws Exception {
        Result<String> _url = new Result<>();

        // 检查图形验证码
        if (requireCaptcha) {
            Result<Void> _success = validateCaptcha(data);
            if (!_success.isOK()) {
                return _url.pack(_success);
            }
        }

        String _username = data.get("username").getAsString();
        String _password = data.get("password").getAsString();

        if (decryptRsa) {
            _password = CryptoUtil.decryptRsa(_password, rsaPrivateKey);
        }

        boolean _rememberMe = Optional.ofNullable(data.get("rememberMe"))
                .map(i -> !i.isJsonNull() && i.getAsBoolean())
                .orElse(false);
        String _redirect = Optional.ofNullable(data.get("redirect"))
                .map(i -> i.isJsonNull() ? null : i.getAsString())
                .orElse(null);

        // 登录
        AuthenticationToken _token = createToken(realmType, _username, _password, _rememberMe, request.getRemoteHost());
        return login(request, _token, _redirect);
    }

    public Result<String> login(HttpServletRequest request, AuthenticationToken token, String redirectUrl) throws UnsupportedEncodingException {
        Result<String> _url = new Result<>();

        Subject _subject = SecurityUtils.getSubject();

        // 过滤重复登录请求
        if (_subject.isAuthenticated() || _subject.isRemembered()) {
            _url.setCode("001_P_011");
            return _url;
        }

        // 登录
        login(token);

        if (StringUtils.hasLength(redirectUrl)) {
            _url.data = URLDecoder.decode(redirectUrl, StandardCharsets.UTF_8.name());
        } else {
            SavedRequest _request = WebUtils.getSavedRequest(request);
            _url.data = _request == null ? null : _request.getRequestUrl();
        }

        return _url;
    }

    public void login(AuthenticationToken token) {
        Subject _subject = SecurityUtils.getSubject();

        // 登录
        _subject.login(token);

        if (token instanceof CMPassportToken) {
            _subject.getSession().setAttribute(REALM_SESSION_ATTRIBUTE, CMPassportAuthorizingRealm.REALM_TYPE);
        } else if (token instanceof ConfigUserToken) {
            _subject.getSession().setAttribute(REALM_SESSION_ATTRIBUTE, ConfigUserAuthorizingRealm.REALM_TYPE);
        } else if (token instanceof SMSToken) {
            _subject.getSession().setAttribute(REALM_SESSION_ATTRIBUTE, SMSAuthorizingRealm.REALM_TYPE);
        } else if (token instanceof YzyToken) {
            _subject.getSession().setAttribute(REALM_SESSION_ATTRIBUTE, YzyAuthorizingRealm.REALM_TYPE);
        } else if (token instanceof WxCpToken) {
            _subject.getSession().setAttribute(REALM_SESSION_ATTRIBUTE, WxCpAuthorizingRealm.REALM_TYPE);
        } else if (token instanceof DefaultUsernamePasswordToken) {
            _subject.getSession().setAttribute(REALM_SESSION_ATTRIBUTE, UsernamePasswordAuthorizingRealm.REALM_TYPE);
        }
    }

    public void logout(User user) {
        // 设置超时，但需等待验证会话
        /* i.setTimeout(0);
                    sessionDAO.update(i); */
        // 删除会话，则无法触发事件
        sessionDAO.getActiveSessions().stream()
                .filter(i -> i.getAttribute(DefaultSubjectContext.PRINCIPALS_SESSION_KEY) instanceof SimplePrincipalCollection)
                .filter(i -> ((SimplePrincipalCollection) i.getAttribute(DefaultSubjectContext.PRINCIPALS_SESSION_KEY)).getPrimaryPrincipal() instanceof User)
                .filter(i -> Objects.equals(user.getId(), ((User) ((SimplePrincipalCollection) i.getAttribute(DefaultSubjectContext.PRINCIPALS_SESSION_KEY)).getPrimaryPrincipal()).getId()))
                .forEach(sessionDAO::delete);

        // 设置下线
        userRepository.logout(user.getId());
    }

    public Result<Void> validateCaptcha(JsonObject data) {
        Result<Void> _success = new Result<>();

        // 检查图形验证码
        JsonObject _captcha = data.get("captcha").getAsJsonObject();
        String _text = _captcha.get("text").getAsString();

        String _cached = Optional.ofNullable(SecurityUtils.getSubject().getSession().getAttribute(CAPTCHA_SESSION_ATTRIBUTE))
                .map(Object::toString).orElse(null);
        if (!StringUtils.hasLength(_cached) || !_cached.equalsIgnoreCase(_text)) {
            _success.setCode("001_P_001");
        }

        return _success;
    }

    protected AuthenticationToken createToken(String realmType, String username, String password, boolean rememberMe, String host) {
        return SMSAuthorizingRealm.REALM_TYPE.equals(realmType) ? new SMSToken(username, password, rememberMe, host) : new DefaultUsernamePasswordToken(username, password, rememberMe, host);
    }

}