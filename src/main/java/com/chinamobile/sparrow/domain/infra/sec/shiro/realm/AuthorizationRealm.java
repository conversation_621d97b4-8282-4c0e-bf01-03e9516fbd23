package com.chinamobile.sparrow.domain.infra.sec.shiro.realm;

import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.infra.sec.shiro.token.CMPassportToken;
import com.chinamobile.sparrow.domain.infra.sec.shiro.token.SMSToken;
import com.chinamobile.sparrow.domain.model.sec.Permission;
import com.chinamobile.sparrow.domain.model.sec.Role;
import com.chinamobile.sparrow.domain.model.sys.User;
import com.chinamobile.sparrow.domain.repository.sec.PermissionRepository;
import com.chinamobile.sparrow.domain.repository.sec.RoleRepository;
import com.chinamobile.sparrow.domain.repository.sys.UserRepository;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.authc.*;
import org.apache.shiro.authz.AuthorizationInfo;
import org.apache.shiro.authz.SimpleAuthorizationInfo;
import org.apache.shiro.realm.AuthorizingRealm;
import org.apache.shiro.subject.PrincipalCollection;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

public abstract class AuthorizationRealm extends AuthorizingRealm {

    protected final Integer maxAttempts;
    protected final UserRepository userRepository;
    protected final RoleRepository roleRepository;
    protected final PermissionRepository permissionRepository;
    protected final ENUM_PRINCIPAL_FIELD principalField;
    protected final Logger logger;

    protected AuthorizationRealm(Integer maxAttempts, UserRepository userRepository, RoleRepository roleRepository, PermissionRepository permissionRepository, ENUM_PRINCIPAL_FIELD principalField) {
        this.maxAttempts = maxAttempts;
        this.userRepository = userRepository;
        this.roleRepository = roleRepository;
        this.permissionRepository = permissionRepository;
        this.principalField = principalField == null ? ENUM_PRINCIPAL_FIELD.ACCOUNT : principalField;
        this.logger = LoggerFactory.getLogger(this.getClass());
    }

    @Override
    public boolean[] isPermitted(PrincipalCollection subjectIdentifier, String... permissions) {
        return super.isPermitted(subjectIdentifier, permissions);
    }

    @Override
    protected AuthorizationInfo doGetAuthorizationInfo(PrincipalCollection principals) {
        User _user = (User) SecurityUtils.getSubject().getPrincipal();

        SimpleAuthorizationInfo _authorization = new SimpleAuthorizationInfo();

        // 设置角色
        List<Role> _roles = roleRepository.findUserRoles(_user.getId());
        if (!_roles.isEmpty()) {
            _authorization.addRoles(_roles.stream()
                    .map(Role::getName)
                    .collect(Collectors.toList()));
        }

        // 设置权限
        List<Permission> _permissions = permissionRepository.findUserPermissions(_user.getId());
        if (!_permissions.isEmpty()) {
            _authorization.addStringPermissions(_permissions.stream()
                    .map(Permission::getName)
                    .collect(Collectors.toList()));
        }

        return _authorization;
    }

    @Override
    protected AuthenticationInfo doGetAuthenticationInfo(AuthenticationToken token) throws AuthenticationException {
        if (token.getPrincipal() == null) {
            return null;
        }

        String _key = getPrincipal(token);
        if (!StringUtils.hasLength(_key)) {
            throw new UnknownAccountException();
        }

        Result<User> _user;
        switch (principalField) {
            case ID:
                _user = userRepository.getByIdOrAccountOrMp(_key, true);
                break;
            case MP:
                _user = userRepository.getByMp(_key, true);
                break;
            default:
                _user = userRepository.getByAccount(_key, true);
                break;
        }

        if (_user.isOK()) {
            if (maxAttempts != null && Optional.ofNullable(_user.data.getLoginAttempts())
                    .filter(i -> i >= maxAttempts)
                    .isPresent()) {
                throw new ExcessiveAttemptsException();
            }

            if (_user.data.getIsLocked()) {
                throw new LockedAccountException();
            }

            return getAuthenticationInfo(token, _user.data);
        }
        // 短信验证码和移动认证支持创建虚拟用户
        else if (token instanceof SMSToken || token instanceof CMPassportToken) {
            _user.data = new User();
            _user.data.setId(_key);
            _user.data.setAccount(_key);
            _user.data.setMp(_key);

            return getAuthenticationInfo(token, _user.data);
        }

        throw new UnknownAccountException();
    }

    abstract protected String getPrincipal(AuthenticationToken token);

    abstract protected AuthenticationInfo getAuthenticationInfo(AuthenticationToken token, User user);

    public enum ENUM_PRINCIPAL_FIELD {
        ID,
        ACCOUNT,
        MP
    }

}