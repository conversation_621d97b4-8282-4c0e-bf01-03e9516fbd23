package com.chinamobile.sparrow.domain.infra.code;

import com.chinamobile.sparrow.domain.util.I18NUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.util.StringUtils;

public class Result<T> {

    public static final String APP_ID;

    // 内置代码
    public static final String OK;
    public static final String UNKNOWN;
    public static final String SERVICE_UNKNOWN;
    public static final String DATA_VALIDATE_ERROR;
    public static final String DATA_ACCESS_DENY;
    public static final String DATABASE_RECORD_NOT_FOUND;
    public static final String DATABASE_RECORD_ALREADY_EXIST;
    public static final String DATABASE_UNKNOWN;

    protected static final Logger LOGGER = LoggerFactory.getLogger(Result.class);

    static {
        // 设置应用编码
        String _appId;
        try {
            _appId = I18NUtil.getMessage("APP_ID", null, LocaleContextHolder.getLocale());
        } catch (Throwable e) {
            _appId = "999";
        }
        APP_ID = _appId;

        // 定义内置编码
        OK = "OK";
        UNKNOWN = "999_B_999";
        SERVICE_UNKNOWN = "999_N_999";
        DATA_VALIDATE_ERROR = "999_P_001";
        DATA_ACCESS_DENY = "999_P_011";
        DATABASE_RECORD_NOT_FOUND = "999_D_001";
        DATABASE_RECORD_ALREADY_EXIST = "999_D_002";
        DATABASE_UNKNOWN = "999_D_999";
    }

    public String message;
    public T data;
    String code;

    public Result() {
        this.code = OK;
    }

    public Result(ENUM_ERROR type, int code) {
        setCode(type, code);
    }

    public Result(String code) {
        setCode(code);
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        setCode(code, null);
    }

    public void setCode(String code, Object[] args) {
        this.code = code.toUpperCase();
        if (OK.equals(this.code)) {
            return;
        }

        // 设置错误消息
        try {
            this.message = I18NUtil.getMessage(this.code, args, LocaleContextHolder.getLocale());
        } catch (Throwable e) {
            this.message = this.code;

            LOGGER.debug("设置message{}失败", code, e);
        }

        // 编入应用编码
        this.code = String.format("%s_%s", APP_ID, this.code);
    }

    public void setCode(ENUM_ERROR type, int code) {
        setCode(type, code, null);
    }

    public void setCode(ENUM_ERROR type, int code, Object[] args) {
        String _module = "UNKNOWN";

        try {
            for (StackTraceElement i : Thread.currentThread().getStackTrace()) {
                Class<?> _class = Class.forName(i.getClassName());

                ErrorCode _annotation = _class.getAnnotation(ErrorCode.class);
                if (_annotation != null && StringUtils.hasLength(_annotation.module())) {
                    _module = _annotation.module().toUpperCase();

                    break;
                }
            }
        } catch (Throwable e) {
            LOGGER.debug("设置code失败", e);
        }

        this.code = String.format("%s_%s_%s", _module, type.toString(), String.format("%03d", code));

        setCode(this.code, args);
    }

    public boolean isOK() {
        return OK.equals(this.code);
    }

    public <T2> Result<T> pack(Result<T2> result) {
        this.code = result.code;
        this.message = result.message;
        this.data = null;

        return this;
    }

    // 错误类型
    public enum ENUM_ERROR {
        /**
         * 业务错误
         */
        B(0),
        /**
         * 数据库错误
         */
        D(1),
        /**
         * 文件错误
         */
        F(2),
        /**
         * HSF错误
         */
        H(3),
        /**
         * 参数错误
         */
        P(4),
        /**
         * 网络错误
         */
        N(5),
        /**
         * TAIR错误
         */
        T(6);

        final int value;

        ENUM_ERROR(int value) {
            this.value = value;
        }

        public static ENUM_ERROR valueOf(int value) {
            switch (value) {
                case 0:
                    return B;
                case 1:
                    return D;
                case 2:
                    return F;
                case 3:
                    return H;
                case 4:
                    return P;
                case 5:
                    return N;
                case 6:
                    return T;
                default:
                    return null;
            }
        }

        public int value() {
            return this.value;
        }
    }

}