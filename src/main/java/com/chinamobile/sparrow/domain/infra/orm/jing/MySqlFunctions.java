package com.chinamobile.sparrow.domain.infra.orm.jing;

import java.util.Date;

public class MySqlFunctions {

    public static String dateFormat(Date date, String format) {
        return null;
    }

    public static Integer hour(Date data) {
        return null;
    }

    public static Integer minute(Date data) {
        return null;
    }

    public static String substr(String str, int start, int length) {
        return null;
    }

    public static String substringIndex(String str, String delimiter, int index) {
        return null;
    }

    public static boolean jsonContains(String field, String str) {
        return false;
    }

    public static String jsonExtract(String field, String str) {
        return null;
    }

}