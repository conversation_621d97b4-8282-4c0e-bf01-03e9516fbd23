package com.chinamobile.sparrow.domain.infra.log;

import com.chinamobile.sparrow.domain.util.ConfigUtil;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.jdbc.datasource.DriverManagerDataSource;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.Objects;

public class Log4j2Configurer {

    public static Connection getDatabaseConnection() throws SQLException {
        String _path = ConfigUtil.getString("spring.datasource.main.config");
        Resource _yml = new FileSystemResource(_path);

        DriverManagerDataSource _dataSource = new DriverManagerDataSource();
        _dataSource.setDriverClassName(Objects.requireNonNull(ConfigUtil.getString("dataSources.main.dataSourceClassName", _yml)));
        _dataSource.setUrl(ConfigUtil.getString("dataSources.main.jdbcUrl", _yml));
        _dataSource.setUsername(ConfigUtil.getString("dataSources.main.username", _yml));
        _dataSource.setPassword(ConfigUtil.getString("dataSources.main.password", _yml));

        return _dataSource.getConnection();
    }

}