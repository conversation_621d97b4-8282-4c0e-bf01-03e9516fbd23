package com.chinamobile.sparrow.domain.infra.orm.hibernate.dialect;

import org.hibernate.dialect.MySQL5InnoDBDialect;
import org.hibernate.dialect.function.StandardSQLFunction;
import org.hibernate.type.StandardBasicTypes;

public class MySQLInnoDBDialect extends MySQL5InnoDBDialect {

    public MySQLInnoDBDialect() {
        super();

        registerFunction("hour", new StandardSQLFunction("hour", StandardBasicTypes.INTEGER));

        registerFunction("minute", new StandardSQLFunction("minute", StandardBasicTypes.INTEGER));

        registerFunction("substr", new StandardSQLFunction("substr", StandardBasicTypes.STRING));

        registerFunction("substring_index", new StandardSQLFunction("substring_index", StandardBasicTypes.STRING));

        registerFunction("json_contains", new StandardSQLFunction("json_contains", StandardBasicTypes.BOOLEAN));

        registerFunction("json_extract", new StandardSQLFunction("json_extract", StandardBasicTypes.STRING));
    }

}