package com.chinamobile.sparrow.domain.infra.log;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import javax.persistence.Transient;
import java.util.Date;

@MappedSuperclass
public abstract class AbstractLog {

    @Id
    long id;

    // 日志级别
    @Column(length = 10)
    String level;

    // 事务
    @Column(length = 36)
    String transactionId;

    String stack;

    @Column(columnDefinition = "text")
    String method;

    @Column(columnDefinition = "text")
    String request;

    @Column(columnDefinition = "text")
    String response;

    @Column(length = 128)
    String ip;

    @Column(length = 36)
    String actorId;

    @Transient
    String account;

    @Transient
    String actorName;

    @Column(length = 32)
    String realmType;

    @Column(columnDefinition = "DATETIME(3)")
    Date startTime;

    @Column(columnDefinition = "DATETIME(3)")
    Date endTime;

    /*
    执行时间
     */
    @Transient
    long period;

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public String getLevel() {
        return level;
    }

    public void setLevel(String level) {
        this.level = level;
    }

    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    public String getStack() {
        return stack;
    }

    public void setStack(String stack) {
        this.stack = stack;
    }

    public String getMethod() {
        return method;
    }

    public void setMethod(String method) {
        this.method = method;
    }

    public String getRequest() {
        return request;
    }

    public void setRequest(String request) {
        this.request = request;
    }

    public String getResponse() {
        return response;
    }

    public void setResponse(String response) {
        this.response = response;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public String getActorId() {
        return actorId;
    }

    public void setActorId(String actorId) {
        this.actorId = actorId;
    }

    public String getAccount() {
        return account;
    }

    public void setAccount(String account) {
        this.account = account;
    }

    public String getActorName() {
        return actorName;
    }

    public void setActorName(String actorName) {
        this.actorName = actorName;
    }

    public String getRealmType() {
        return realmType;
    }

    public void setRealmType(String auth) {
        this.realmType = auth;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public long getPeriod() {
        return period;
    }

    public void setPeriod(long period) {
        this.period = period;
    }

}