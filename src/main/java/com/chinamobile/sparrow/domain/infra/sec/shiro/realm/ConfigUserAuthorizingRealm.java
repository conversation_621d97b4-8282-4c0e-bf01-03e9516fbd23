package com.chinamobile.sparrow.domain.infra.sec.shiro.realm;

import com.chinamobile.sparrow.domain.infra.sec.shiro.token.ConfigUserToken;
import com.chinamobile.sparrow.domain.model.sys.User;
import com.chinamobile.sparrow.domain.repository.sec.PermissionRepository;
import com.chinamobile.sparrow.domain.repository.sec.RoleRepository;
import com.chinamobile.sparrow.domain.repository.sys.UserRepository;
import org.apache.shiro.authc.AuthenticationInfo;
import org.apache.shiro.authc.AuthenticationToken;
import org.apache.shiro.authc.SimpleAuthenticationInfo;

public class ConfigUserAuthorizingRealm extends AuthorizationRealm {

    public static final String REALM_TYPE = "CONFIG";

    public ConfigUserAuthorizingRealm(Integer maxAttempts, UserRepository userRepository, RoleRepository roleRepository, PermissionRepository permissionRepository) {
        super(maxAttempts, userRepository, roleRepository, permissionRepository, ENUM_PRINCIPAL_FIELD.ACCOUNT);
    }

    @Override
    public boolean supports(AuthenticationToken token) {
        return token instanceof ConfigUserToken;
    }

    @Override
    protected String getPrincipal(AuthenticationToken token) {
        return token.getPrincipal().toString();
    }

    @Override
    protected AuthenticationInfo getAuthenticationInfo(AuthenticationToken token, User user) {
        return new SimpleAuthenticationInfo(user, ConfigUserToken.DEFAULT_PASSWORD, this.getClass().toString());
    }

}