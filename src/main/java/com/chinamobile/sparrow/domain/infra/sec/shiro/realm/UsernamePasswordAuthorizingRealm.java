package com.chinamobile.sparrow.domain.infra.sec.shiro.realm;

import com.chinamobile.sparrow.domain.infra.sec.shiro.token.DefaultUsernamePasswordToken;
import com.chinamobile.sparrow.domain.model.sys.User;
import com.chinamobile.sparrow.domain.repository.sec.PermissionRepository;
import com.chinamobile.sparrow.domain.repository.sec.RoleRepository;
import com.chinamobile.sparrow.domain.repository.sys.UserRepository;
import org.apache.shiro.authc.AuthenticationInfo;
import org.apache.shiro.authc.AuthenticationToken;
import org.apache.shiro.authc.IncorrectCredentialsException;
import org.apache.shiro.authc.SimpleAuthenticationInfo;
import org.springframework.util.StringUtils;

public class UsernamePasswordAuthorizingRealm extends AuthorizationRealm {

    public static final String REALM_TYPE = "PASSWORD";

    public UsernamePasswordAuthorizingRealm(Integer maxAttempts, UserRepository userRepository, RoleRepository roleRepository, PermissionRepository permissionRepository) {
        super(maxAttempts, userRepository, roleRepository, permissionRepository, ENUM_PRINCIPAL_FIELD.ACCOUNT);
    }

    @Override
    public boolean supports(AuthenticationToken token) {
        return token instanceof DefaultUsernamePasswordToken;
    }

    @Override
    protected String getPrincipal(AuthenticationToken token) {
        return token.getPrincipal().toString();
    }

    @Override
    protected AuthenticationInfo getAuthenticationInfo(AuthenticationToken token, User user) {
        // 密码不允许为空
        if (!StringUtils.hasLength(user.getPassword())) {
            throw new IncorrectCredentialsException();
        }

        return new SimpleAuthenticationInfo(user, user.getPassword(), this.getClass().toString());
    }

}