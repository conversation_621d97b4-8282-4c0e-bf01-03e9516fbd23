package com.chinamobile.sparrow.domain.infra.sec.shiro.realm;

import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.infra.sec.shiro.token.YzyToken;
import com.chinamobile.sparrow.domain.model.sys.User;
import com.chinamobile.sparrow.domain.repository.sec.PermissionRepository;
import com.chinamobile.sparrow.domain.repository.sec.RoleRepository;
import com.chinamobile.sparrow.domain.repository.sys.UserRepository;
import com.chinamobile.sparrow.domain.service.wx.cp.AccessFacade;
import org.apache.shiro.authc.*;

public class YzyAuthorizingRealm extends AuthorizationRealm {

    public static final String REALM_TYPE = "WXYZY";

    protected final AccessFacade accessFacade;

    public YzyAuthorizingRealm(Integer maxAttempts, UserRepository userRepository, RoleRepository roleRepository, PermissionRepository permissionRepository, AccessFacade accessFacade) {
        super(maxAttempts, userRepository, roleRepository, permissionRepository, null);
        this.accessFacade = accessFacade;
    }

    @Override
    public boolean supports(AuthenticationToken token) {
        return token instanceof YzyToken;
    }

    @Override
    protected AuthenticationInfo doGetAuthenticationInfo(AuthenticationToken token) throws AuthenticationException {
        String _code = token.getPrincipal().toString();

        com.chinamobile.sparrow.domain.service.wx.cp.lang.User _dto;
        try {
            _dto = accessFacade.getUser(_code);
        } catch (Throwable e) {
            logger.error("粤政易授权码验证失败", e);

            throw new AuthenticationException(e);
        }

        if (_dto == null) {
            throw new UnknownAccountException("粤政易授权码错误，登录失败");
        }

        Result<User> _user = userRepository.getBriefByYzyId(_dto.id, true);
        if (_user.isOK()) {
            return new SimpleAuthenticationInfo(_user.data, YzyToken.DEFAULT_PASSWORD, this.getClass().toString());
        } else {
            throw new UnknownAccountException(String.format("您的粤政易账号[%s]未关联到应用账号，登录失败", _dto.id));
        }
    }

    @Override
    protected String getPrincipal(AuthenticationToken token) {
        return null;
    }

    @Override
    protected AuthenticationInfo getAuthenticationInfo(AuthenticationToken token, User user) {
        return null;
    }

}