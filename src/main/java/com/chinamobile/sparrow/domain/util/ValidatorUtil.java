package com.chinamobile.sparrow.domain.util;

import org.springframework.util.StringUtils;

public class ValidatorUtil {

    public static boolean isMp(String str) {
        return StringUtils.hasLength(str) && str.matches("^1\\d{10}$");
    }

    public static boolean isEmail(String str) {
        return StringUtils.hasLength(str) && str.matches("^\\w+([-+.]\\w+)*@\\w+([-.]\\w+)*\\.\\w+([-.]\\w+)*$");
    }

}