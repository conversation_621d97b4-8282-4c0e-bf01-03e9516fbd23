package com.chinamobile.sparrow.domain.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.Inet4Address;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.util.Enumeration;
import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;

public class IdWorker {

    static List<IdWorker> idWorkers = new CopyOnWriteArrayList<>();

    static Logger logger = LoggerFactory.getLogger(IdWorker.class);

    long dataCenterId;
    long workerId;
    long sequence = 0L;

    long twepoch = 1585644268888L;
    long dataCenterIdBits = 5L;
    long workerIdBits = 5L;
    long sequenceBits = 12L;

    long maxDataCenterId = ~(-1L << dataCenterIdBits);
    long maxWorkerId = ~(-1L << workerIdBits);
    long workerIdShift = sequenceBits;
    long dataCenterIdShift = sequenceBits + workerIdBits;
    long timestampLeftShift = sequenceBits + workerIdBits + dataCenterIdBits;
    long sequenceMask = ~(-1L << sequenceBits);
    long lastTimestamp = -1L;


    IdWorker(long workerId, long dataCenterId) {
        if (workerId > maxWorkerId || workerId < 0) {
            throw new IllegalArgumentException(String.format("worker Id can't be greater than %d or less than 0", maxWorkerId));
        }

        if (dataCenterId > maxDataCenterId || dataCenterId < 0) {
            throw new IllegalArgumentException(String.format("datacenter Id can't be greater than %d or less than 0", maxDataCenterId));
        }

        this.workerId = workerId;
        this.dataCenterId = dataCenterId;
    }

    public static IdWorker getInstance() {
        if (idWorkers.isEmpty()) {
            try {
                Enumeration<NetworkInterface> _networks = NetworkInterface.getNetworkInterfaces();

                while (_networks.hasMoreElements()) {
                    NetworkInterface _network = _networks.nextElement();
                    Enumeration<InetAddress> _addresses = _network.getInetAddresses();
                    while (_addresses.hasMoreElements()) {
                        InetAddress _address = _addresses.nextElement();
                        if (_address instanceof Inet4Address) {
                            logger.debug(String.format("获取到IPv4地址[%s]", _address.getHostAddress()));

                            // 忽略本机地址
                            if ("127.0.0.1".equals(_address.getHostAddress())) {
                                continue;
                            }

                            String[] _nums = _address.getHostAddress().split("\\.");
                            if (_nums.length == 4) {
                                int _num = Integer.parseInt(_nums[3]);
                                idWorkers.add(new IdWorker(_num % 32, _num / 32));

                                logger.debug(String.format("设置dataCenterId[%s]和workerId[%s]", idWorkers.get(0).dataCenterId, idWorkers.get(0).workerId));

                                return idWorkers.get(0);
                            }
                        }
                    }
                }
            } catch (Throwable e) {
                logger.debug("获取网络地址失败", e);
            }
        }

        return getInstance(0, 0);
    }

    public static IdWorker getInstance(int workerId, int dataCenterId) {
        IdWorker _instance = idWorkers.stream()
                .filter(i -> workerId == i.getWorkerId() && dataCenterId == i.getDataCenterId())
                .findFirst().orElse(null);

        if (_instance == null) {
            _instance = new IdWorker(workerId, dataCenterId);
            idWorkers.add(_instance);
        }

        return _instance;
    }

    public synchronized long nextId() {
        long _timestamp = timeGen();
        if (_timestamp < lastTimestamp) {
            throw new RuntimeException(String.format("Clock moved backwards. Refusing to generate id for %d milliseconds", lastTimestamp - _timestamp));
        }

        // 如果在同一个毫秒内，又发送了一个请求生成一个id，则把seqence序号给递增1，最多就是4096
        if (lastTimestamp == _timestamp) {
            sequence = (sequence + 1) & sequenceMask;
            if (sequence == 0) {
                _timestamp = tilNextMillis(lastTimestamp);
            }
        } else {
            sequence = 0;
        }

        lastTimestamp = _timestamp;

        return ((_timestamp - twepoch) << timestampLeftShift) | (dataCenterId << dataCenterIdShift) | (workerId << workerIdShift) | sequence;
    }

    public long getWorkerId() {
        return workerId;
    }

    public long getDataCenterId() {
        return dataCenterId;
    }

    public long getTimestamp() {
        return System.currentTimeMillis();
    }

    // 当某一毫秒的时间，产生的id数超过4095，系统会进入等待，直到下一毫秒，系统继续产生id
    long tilNextMillis(long lastTimestamp) {
        long _timestamp = timeGen();

        while (_timestamp <= lastTimestamp) {
            _timestamp = timeGen();
        }

        return _timestamp;
    }

    // 获取当前时间戳
    long timeGen() {
        return System.currentTimeMillis();
    }

}
