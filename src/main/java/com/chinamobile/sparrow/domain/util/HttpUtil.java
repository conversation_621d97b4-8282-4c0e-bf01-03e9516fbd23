package com.chinamobile.sparrow.domain.util;

import com.chinamobile.sparrow.domain.infra.code.Result;
import com.google.gson.reflect.TypeToken;
import okhttp3.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpHeaders;
import org.springframework.util.CollectionUtils;
import org.springframework.util.MimeTypeUtils;

import javax.net.ssl.*;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.PrintWriter;
import java.net.MalformedURLException;
import java.net.Proxy;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.security.KeyManagementException;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.security.cert.X509Certificate;
import java.util.HashMap;
import java.util.Map;
import java.util.function.Supplier;

// 网络请求工具类
public class HttpUtil {

    protected static final Logger LOGGER = LoggerFactory.getLogger(HttpUtil.class);

    public OkHttpClient client = null;
    protected CookieJarImpl cookieJar;
    protected Proxy proxy = null;

    public HttpUtil(ConnectionPool connectionPool, Proxy proxy) {
        this(connectionPool, null, proxy);
    }

    public HttpUtil(ConnectionPool connectionPool, CookieJarImpl cookieJar, Proxy proxy) {
        this.cookieJar = cookieJar == null ? new CookieJarImpl() : cookieJar;

        if (proxy != null) {
            this.proxy = proxy;
        }

        init(connectionPool);
    }

    public static void writeJson(HttpServletResponse response, Object obj) {
        try (PrintWriter output = response.getWriter()) {
            String _json = ConverterUtil.toJson(obj);

            response.setCharacterEncoding(StandardCharsets.UTF_8.name());
            response.setContentType(MimeTypeUtils.APPLICATION_JSON_VALUE);
            output.write(_json);
        } catch (IOException e) {
            LOGGER.error("写响应失败", e);
        }
    }

    protected void init(ConnectionPool connectionPool) {
        if (cookieJar == null) {
            cookieJar = new CookieJarImpl();
        }
        OkHttpClient.Builder _builder = new OkHttpClient.Builder().cookieJar(cookieJar);

        // 设置代理
        if (proxy != null) {
            _builder.proxy(proxy);
        }

        this.client = _builder.connectionPool(connectionPool == null ? new ConnectionPool() : connectionPool)
                .sslSocketFactory(createUnsafeSSLContext().getSocketFactory(), new TrustAllCertsManager())
                .hostnameVerifier(new TrustAllHostnameVerifier())
                .build();
    }

    public Result<String> get(String url, Map<String, String> headers) {
        Result<String> _body = new Result<>();

        Request.Builder _builder = new Request.Builder().url(url);
        _builder = setHeaders(_builder, headers);

        try {
            Response _response = client.newCall(_builder.build()).execute();

            if (_response.isSuccessful()) {
                _body.data = _response.body() == null ? null : _response.body().string();
            } else {
                _body.setCode(String.valueOf(_response.code()));
                _body.message = _response.message();
            }
        } catch (Throwable e) {
            LOGGER.debug(String.format("GET请求%s失败", url), e);

            _body.setCode(Result.SERVICE_UNKNOWN, new Object[]{e.getMessage()});
        }

        return _body;
    }

    public <T1, T2> Result<T2> request(String url, Map<String, String> headers, T1 data, TypeToken<T2> type) throws IOException {
        return readResponseToObject(() -> {
            Result<Response> _response = new Result<>();

            String _json = ConverterUtil.toJson(data == null ? new HashMap<>() : data);
            RequestBody _body = RequestBody.create(MediaType.parse(MimeTypeUtils.APPLICATION_JSON_VALUE), _json);

            try {
                _response.data = request(url, headers, _body);
            } catch (Throwable e) {
                LOGGER.debug(String.format("POST请求%s失败", url), e);

                _response.setCode(Result.SERVICE_UNKNOWN, new Object[]{e.getMessage()});
            }

            return _response;
        }, type);
    }

    public <T> Result<T> form(String url, Map<String, String> headers, Map<String, String> data, TypeToken<T> type) throws IOException {
        return readResponseToObject(() -> {
            Result<Response> _response = new Result<>();

            FormBody.Builder _body = new FormBody.Builder();
            if (data != null) {
                for (String i : data.keySet()) {
                    _body.add(i, data.get(i));
                }
            }

            try {
                _response.data = request(url, headers, _body.build());
            } catch (Throwable e) {
                LOGGER.debug(String.format("表单提交到%s失败", url), e);

                _response.setCode(Result.SERVICE_UNKNOWN, new Object[]{e.getMessage()});
            }

            return _response;
        }, type);
    }

    public Response request(String url, Map<String, String> headers, RequestBody body) throws Exception {
        Request.Builder _builder = new Request.Builder()
                .url(url);

        // 设置头部
        _builder = setHeaders(_builder, headers);

        // 设置请求体
        _builder = _builder.post(body);

        return client.newCall(_builder.build()).execute();
    }

    /**
     * 设置cookie
     *
     * @param url
     * @param cookie
     */
    public void setCookie(String url, Cookie cookie) {
        try {
            URL _url = new URL(url);
            this.cookieJar.setCookie(_url.getHost(), cookie);
        } catch (MalformedURLException e) {
            LOGGER.error("设置cookie失败", e);
        }
    }

    /**
     * 删除cookie
     *
     * @param url
     * @param name
     */
    public void removeCookie(String url, String name) {
        try {
            URL _url = new URL(url);
            this.cookieJar.removeCookie(_url.getHost(), name);
        } catch (MalformedURLException e) {
            LOGGER.error("删除cookie失败", e);
        }
    }

    /**
     * 设置头部
     *
     * @param builder
     * @param headers
     * @return
     */
    protected Request.Builder setHeaders(Request.Builder builder, Map<String, String> headers) {
        // 设置默认的用户代理
        builder.removeHeader(HttpHeaders.USER_AGENT)
                .addHeader(HttpHeaders.USER_AGENT, "Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/69.0.3497.100 Safari/537.36");

        if (!CollectionUtils.isEmpty(headers)) {
            for (String i : headers.keySet()) {
                builder.addHeader(i, headers.get(i));
            }
        }

        return builder;
    }

    /**
     * 返回对象
     *
     * @param supplier
     * @param type
     * @param <T>
     * @return
     * @throws IOException
     */
    public <T> Result<T> readResponseToObject(Supplier<Result<Response>> supplier, TypeToken<T> type) throws IOException {
        Result<T> _body = new Result<>();

        Result<Response> _response = supplier.get();
        if (!_response.isOK()) {
            return _body.pack(_response);
        }

        if (_response.data.isSuccessful()) {
            String _str = _response.data.body() == null ? null : _response.data.body().string();
            _body.data = type.getType().equals(String.class) ? (T) _str : ConverterUtil.json2Object(_str, type.getType());
        } else {
            _body.setCode(String.valueOf(_response.data.code()));
            _body.message = _response.data.message();
        }

        return _body;
    }

    /**
     * 返回响应流
     *
     * @param supplier
     * @return
     */
    public Result<InputStream> readResponseToStream(Supplier<Result<Response>> supplier) {
        Result<InputStream> _stream = new Result<>();

        Result<Response> _response = supplier.get();
        if (!_response.isOK()) {
            return _stream.pack(_response);
        }

        if (_response.data.isSuccessful()) {
            _stream.data = _response.data.body() == null ? null : _response.data.body().byteStream();
        } else {
            _stream.setCode(String.valueOf(_response.data.code()));
            _stream.message = _response.data.message();
        }

        return _stream;
    }

    public static SSLContext createUnsafeSSLContext() {
        SSLContext _sslContext;
        try {
            _sslContext = SSLContext.getInstance("SSL");
            _sslContext.init(null, new TrustManager[]{new TrustAllCertsManager()}, new SecureRandom());
            return _sslContext;
        } catch (NoSuchAlgorithmException | KeyManagementException e) {
            throw new RuntimeException(e);
        }
    }

    public static class TrustAllCertsManager implements X509TrustManager {

        @Override
        public void checkClientTrusted(X509Certificate[] x509Certificates, String s) {
        }

        @Override
        public void checkServerTrusted(X509Certificate[] x509Certificates, String s) {
        }

        @Override
        public X509Certificate[] getAcceptedIssuers() {
            return new X509Certificate[0];
        }

    }

    public static class TrustAllHostnameVerifier implements HostnameVerifier {

        @Override
        public boolean verify(String s, SSLSession sslSession) {
            return true;
        }

    }

}