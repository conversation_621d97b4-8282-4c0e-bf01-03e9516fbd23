package com.chinamobile.sparrow.domain.util;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import org.json.XML;

import java.lang.reflect.Type;

// 类型转换工具类
public class ConverterUtil {

    public static final Gson gson = createGson();

    static Gson createGson() {
        GsonBuilder _builder = new GsonBuilder()
                .setDateFormat("yyyy-MM-dd HH:mm:ss")
                .serializeNulls();

        return _builder.create();
    }

    public static String byte2Hex(final byte[] bytes) {
        StringBuilder _str = new StringBuilder();

        for (byte aByte : bytes) {
            String _temp = Integer.toHexString(aByte & 0xFF);
            _str.append(_temp.length() == 1 ? "0" : "").append(_temp);
        }

        return _str.toString().toUpperCase();
    }

    public static String toJson(Object data) {
        return gson.toJson(data);
    }

    public static <T> T json2Object(String json, Class<T> type) {
        return gson.fromJson(json, type);
    }

    public static <T> T json2Object(String json, Type type) {
        return gson.fromJson(json, type);
    }

    public static <T> T xml2Object(String xml, Class<T> type) {
        String _json = XML.toJSONObject(xml).toString();
        return json2Object(_json, type);
    }

}