package com.chinamobile.sparrow.domain.util;

import okhttp3.<PERSON>ie;
import okhttp3.<PERSON><PERSON>Jar;
import okhttp3.HttpUrl;
import org.springframework.util.StringUtils;

import java.util.*;

public class CookieJarImpl implements CookieJar {

    protected Map<String, Cookie> cookies = new HashMap<>();

    public void setCookie(String host, Cookie cookie) {
        String _key = String.format("%s@%s", host, cookie.name());
        this.cookies.put(_key, cookie);
    }

    public void removeCookie(String host, String name) {
        String _key = String.format("%s@%s", host, name);
        this.cookies.remove(_key);
    }

    @Override
    public void saveFromResponse(HttpUrl httpUrl, List<Cookie> list) {
        for (Cookie i : list) {
            String _domain = StringUtils.hasLength(i.domain()) ? i.domain() : httpUrl.host();
            String _key = String.format("%s@%s", i.name(), _domain);

            this.cookies.put(_key, i);
        }

    }

    @Override
    public List<Cookie> loadForRequest(HttpUrl httpUrl) {
        List<Cookie> _cookies = new ArrayList<>();
        for (Map.Entry<String, Cookie> i : this.cookies.entrySet()) {
            String _path = i.getKey().substring(i.getKey().lastIndexOf("@") + 1);
            if (Objects.equals(httpUrl.host(), _path)) {
                _cookies.add(i.getValue());
            }
        }

        return _cookies;
    }

}