package com.chinamobile.sparrow.domain.util;

import org.apache.commons.io.FilenameUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.*;
import java.math.BigDecimal;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.function.Consumer;
import java.util.function.Function;

public class POIUtil {

    protected final static String EXT_XLS = "xls";
    protected final static String EXT_XLSX = "xlsx";

    public static List<String[]> readExcel(Function<Cell, String> cell, File file, int sheet) throws IOException {
        try (InputStream stream = Files.newInputStream(Paths.get(file.getPath()))) {
            return readExcel(cell, stream, FilenameUtils.getExtension(file.getName()), sheet);
        }
    }

    public static List<String[]> readExcel(Function<Cell, String> cell, InputStream stream, String extension, int sheet) throws IOException {
        try (Workbook book = readWorkBook(stream, extension)) {
            if (book == null) {
                return null;
            }

            Sheet _sheet = book.getSheetAt(sheet);
            if (_sheet == null) {
                return null;
            }

            int _firstRowNum = _sheet.getFirstRowNum();
            int _lastRowNum = _sheet.getLastRowNum();
            List<String[]> _rows = new ArrayList<>();

            for (int i = _firstRowNum; i <= _lastRowNum; i++) {
                Row row = _sheet.getRow(i);
                if (row == null) {
                    continue;
                }

                int _firstCellNum = row.getFirstCellNum();
                int _lastCellNum = row.getLastCellNum();

                String[] _cells = new String[row.getLastCellNum()];

                for (int j = _firstCellNum; j < _lastCellNum; j++) {
                    Cell _cell = row.getCell(j);
                    _cells[j] = cell == null ? getCellValueByType(_cell) : cell.apply(_cell);
                }

                _rows.add(_cells);
            }

            return _rows;
        }
    }

    public static Workbook readWorkBook(File file) throws IOException {
        try (InputStream stream = Files.newInputStream(Paths.get(file.getPath()))) {
            return readWorkBook(stream, FilenameUtils.getExtension(file.getName()));
        }
    }

    public static Workbook readWorkBook(InputStream stream, String ext) throws IOException {
        switch (ext) {
            case EXT_XLS:
                return new HSSFWorkbook(stream);
            case EXT_XLSX:
                return new XSSFWorkbook(stream);
            default:
                return null;
        }
    }

    public static File exportToWorkbook(Consumer<Workbook> consumer, File file, String extension) throws IOException {
        String _extension = file == null ? extension : FilenameUtils.getExtension(file.getName());

        Workbook _book;
        switch (_extension) {
            case EXT_XLS:
                _book = new HSSFWorkbook();
                break;
            case EXT_XLSX:
                _book = new XSSFWorkbook();
                break;
            default:
                return null;
        }

        consumer.accept(_book);

        File _file = file == null ? File.createTempFile(UUID.randomUUID().toString().replaceAll("-", ""), "." + _extension) : file;
        try (FileOutputStream stream = new FileOutputStream(_file)) {
            _book.write(stream);

            return _file;
        }
    }

    public static String exportToWorkbookBase64String(Consumer<Workbook> consumer, String extension) throws IOException {
        File _file = exportToWorkbook(consumer, null, extension);

        try {
            return FileUtil.readFileToBase64String(_file);
        } finally {
            _file.delete();
        }
    }

    public static byte[] appendRowToExcel(File file, int sheet, List<String> data) throws IOException {
        try (Workbook book = POIUtil.readWorkBook(file)) {
            appendRowTo(book, sheet, data);

            ByteArrayOutputStream _steam = new ByteArrayOutputStream();
            book.write(_steam);

            return _steam.toByteArray();
        }
    }

    public static void appendRowTo(Workbook book, int sheet, List<String> data) {
        if (book == null) {
            return;
        }

        Sheet _sheet = book.getSheetAt(sheet);
        if (_sheet == null) {
            return;
        }

        Row _row = _sheet.createRow(_sheet.getLastRowNum() + 1);
        for (int i = 0; i < data.size(); i++) {
            _row.createCell(i).setCellValue(data.get(i));
        }
    }

    public static byte[] appendColumnToExcel(File file, int sheet, List<String> data) throws IOException {
        try (Workbook book = POIUtil.readWorkBook(file)) {
            appendColumnTo(book, sheet, data);

            ByteArrayOutputStream _steam = new ByteArrayOutputStream();
            book.write(_steam);

            return _steam.toByteArray();
        }
    }

    public static void appendColumnTo(Workbook book, int sheet, List<String> data) {
        if (book == null) {
            return;
        }

        Sheet _sheet = book.getSheetAt(sheet);
        if (_sheet == null) {
            return;
        }

        int _lastRowNum = _sheet.getLastRowNum();
        for (int i = 0; i <= _lastRowNum; i++) {
            Row _row = _sheet.getRow(i);
            if (_row == null) {
                continue;
            }

            if (i >= data.size()) {
                break;
            }

            _row.createCell(_row.getLastCellNum()).setCellValue(data.get(i));
        }
    }
    public static String getCellValueByType(Cell cell) {
        if (cell == null) {
            return null;
        }

        String _value;
        switch (cell.getCellType()) {
            case BOOLEAN: // 布尔值
                _value = String.valueOf(cell.getBooleanCellValue());
                break;
            case FORMULA: // 公式
                try {
                    _value = String.valueOf(getNumericCellValue(cell));
                } catch (IllegalStateException e) {
                    _value = String.valueOf(cell.getRichStringCellValue());
                }
                break;
            case NUMERIC: // 数字
                _value = getNumericCellValue(cell);
                break;
            case STRING: // 字符串
                _value = cell.getStringCellValue();
                break;
            case BLANK: // 空值
            case ERROR: // 故障
            default:
                _value = null;
                break;
        }

        return _value;
    }

    static String getNumericCellValue(Cell cell) {
        if (org.apache.poi.ss.usermodel.DateUtil.isCellDateFormatted(cell)) {
            return DateUtil.toString(cell.getDateCellValue(), "yyyy-MM-dd HH:mm:ss");
        } else {
            return BigDecimal.valueOf(cell.getNumericCellValue()).toPlainString();
        }
    }

}
