package com.chinamobile.sparrow.domain.util;

import ch.ethz.ssh2.*;
import com.chinamobile.sparrow.domain.infra.code.ErrorCode;
import com.chinamobile.sparrow.domain.infra.code.Result;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.io.*;
import java.util.ArrayList;
import java.util.List;
import java.util.function.Function;

@ErrorCode(module = "007")
public class SSHUtil {

    protected final static Logger LOGGER = LoggerFactory.getLogger(SSHUtil.class);

    protected final String DEFAULT_CHARSET = "UTF-8";

    protected final String ip;
    protected final String username;
    protected final char[] pemPrivateKey;
    protected final String password;

    public SSHUtil(String ip, String username, String password) {
        this.ip = ip;
        this.username = username;
        this.pemPrivateKey = null;
        this.password = password;
    }

    public SSHUtil(String ip, String username, char[] pemPrivateKey, String password) {
        this.ip = ip;
        this.username = username;
        this.pemPrivateKey = pemPrivateKey;
        this.password = password;
    }

    public SSHUtil(String ip, String username, File pemFile, String password) throws IOException {
        this.ip = ip;
        this.username = username;

        try (FileReader reader = new FileReader(pemFile); CharArrayWriter writer = new CharArrayWriter()) {
            char[] _buffer = new char[256];

            while (true) {
                int _length = reader.read(_buffer);
                if (_length < 0) {
                    this.pemPrivateKey = writer.toCharArray();
                    break;
                }

                writer.write(_buffer, 0, _length);
            }
        }

        this.password = password;
    }

    public <T> Result<T> connect(Function<Connection, Result<T>> func) {
        Result<T> _response = new Result<>();

        Connection _conn = null;
        try {
            _conn = new Connection(ip);
            _conn.connect();

            boolean _success = false;
            if (pemPrivateKey != null && pemPrivateKey.length > 0) {
                _success = _conn.authenticateWithPublicKey(username, pemPrivateKey, password);
            } else if (StringUtils.hasLength(password)) {
                _success = _conn.authenticateWithPassword(username, password);
            }

            if (!_success) {
                _response.setCode(Result.ENUM_ERROR.N, 1, new Object[]{ip});
                return _response;
            }

            return func.apply(_conn);
        } catch (IOException e) {
            LOGGER.debug(String.format("无法连接到主机[%s]", ip), e);

            _response.setCode(Result.ENUM_ERROR.N, 2, new Object[]{ip, e.getMessage()});
            return _response;
        } finally {
            if (_conn != null) {
                _conn.close();
            }
        }
    }

    public Result<List<String>> execCommand(Connection conn, String command) {
        return execCommand(conn, command, null, null);
    }

    public Result<List<String>> execCommand(Connection conn, String command, Long timeout, String charset) {
        Result<List<String>> _response = new Result<>();

        Session _session = null;
        try {
            _session = conn.openSession();
            _session.execCommand(command, StringUtils.hasLength(charset) ? charset : DEFAULT_CHARSET);
            _session.waitForCondition(ChannelCondition.EXIT_STATUS, timeout == null ? 60000 : timeout);

            List<String> _lines = new ArrayList<>();
            String _line;
            try (BufferedReader err = new BufferedReader(new InputStreamReader(_session.getStderr())); BufferedReader out = new BufferedReader(new InputStreamReader(_session.getStdout()))) {
                while ((_line = err.readLine()) != null) {

                    _lines.add(_line);
                }

                if (!CollectionUtils.isEmpty(_lines)) {
                    _response.setCode(Result.UNKNOWN);
                    _response.message = String.join("\n", _lines);
                    return _response;
                }

                while ((_line = out.readLine()) != null) {
                    _lines.add(_line);
                }

                _response.data = _lines;
                return _response;
            }
        } catch (IOException e) {
            LOGGER.debug(String.format("在主机[%s]运行指令失败", ip), e);

            _response.setCode(Result.ENUM_ERROR.N, 3, new Object[]{ip, command, e.getMessage()});
            return _response;
        } finally {
            if (_session != null) {
                _session.close();
            }
        }
    }

    public Result<List<File>> batchDownload(List<String> remoteFilePaths, String destDirectoryPath) {
        return connect(connection -> {
            Result<List<File>> _files = new Result<>();

            SCPClient _client;
            try {
                _client = connection.createSCPClient();
            } catch (Throwable e) {
                LOGGER.debug(String.format("无法连接到主机[%s]", ip), e);

                _files.setCode(Result.ENUM_ERROR.N, 4, new Object[]{ip, e.getMessage()});
                return _files;
            }

            // 新建本地目录
            File _dir = new File(destDirectoryPath);
            if (!_dir.exists()) {
                _dir.mkdirs();
            }

            String[] _arr;
            String _fileName, _dest;
            byte[] _buffer = new byte[1024];
            int _length;

            _files.data = new ArrayList<>();
            for (String i : remoteFilePaths) {
                _arr = i.split("[/\\\\]");
                _fileName = _arr[_arr.length - 1];
                _dest = destDirectoryPath + File.separator + _fileName;

                try (SCPInputStream input = _client.get(i); FileOutputStream output = new FileOutputStream(_dest)) {
                    while ((_length = input.read(_buffer)) != -1) {
                        output.write(_buffer, 0, _length);
                    }

                    _files.data.add(new File(_dest));
                } catch (Throwable e) {
                    LOGGER.debug(String.format("上传文件[%s]到主机[%s]失败", i, ip), e);

                    _files.data.add(null);
                }
            }

            return _files;
        });
    }

    public Result<List<Boolean>> batchUpload(List<File> files, String remoteDirectoryPath) {
        return connect(connection -> {
            Result<List<Boolean>> _success = new Result<>();

            SCPClient _client;
            try {
                _client = connection.createSCPClient();
            } catch (Throwable e) {
                LOGGER.debug(String.format("无法连接到主机[%s]", ip), e);

                _success.setCode(Result.ENUM_ERROR.N, 4, new Object[]{ip, e.getMessage()});
                return _success;
            }

            // 新建远程目录
            execCommand(connection, "sudo mkdir -p " + remoteDirectoryPath);

            byte[] _buffer = new byte[1024];
            int _length;

            _success.data = new ArrayList<>();
            for (File i : files) {
                try (FileInputStream input = new FileInputStream(i); SCPOutputStream output = _client.put(i.getName(), i.length(), remoteDirectoryPath, null)) {
                    while ((_length = input.read(_buffer)) != -1) {
                        output.write(_buffer, 0, _length);
                    }

                    _success.data.add(true);
                } catch (IOException e) {
                    LOGGER.debug(String.format("从主机[%s]下载文件[%s]失败", ip, i.getName()), e);

                    _success.data.add(false);
                }
            }

            return _success;
        });
    }

}