package com.chinamobile.sparrow.domain.util;

import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.DESKeySpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.KeyFactory;
import java.security.SecureRandom;
import java.security.interfaces.RSAPrivateKey;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.Base64;

// 加解密工具类
public class CryptoUtil {

    public static String encryptDes(String str, String key) throws Exception {
        DESKeySpec _keySpec = new DESKeySpec(key.getBytes(StandardCharsets.UTF_8));
        SecretKey _key = SecretKeyFactory.getInstance("DES").generateSecret(_keySpec);

        Cipher _cipher = Cipher.getInstance("DES");
        _cipher.init(Cipher.ENCRYPT_MODE, _key, new SecureRandom());

        byte[] _bytes = str.getBytes(StandardCharsets.UTF_8);
        _bytes = _cipher.doFinal(_bytes);
        return Base64.getEncoder().encodeToString(_bytes);
    }

    public static String decryptDes(String str, String key) throws Exception {
        DESKeySpec _keySpec = new DESKeySpec(key.getBytes(StandardCharsets.UTF_8));
        SecretKey _key = SecretKeyFactory.getInstance("DES").generateSecret(_keySpec);

        Cipher _cipher = Cipher.getInstance("DES");
        _cipher.init(Cipher.DECRYPT_MODE, _key, new SecureRandom());

        byte[] _bytes = Base64.getDecoder().decode(str.getBytes(StandardCharsets.UTF_8));
        _bytes = _cipher.doFinal(_bytes);
        return new String(_bytes);
    }

    public static String encryptAes(String str, String key) throws Exception {
        byte[] _bytes = key.getBytes(StandardCharsets.UTF_8);
        SecretKeySpec _key = new SecretKeySpec(_bytes, "AES");

        Cipher _cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
        _cipher.init(Cipher.ENCRYPT_MODE, _key);

        _bytes = str.getBytes(StandardCharsets.UTF_8);
        _bytes = _cipher.doFinal(_bytes);
        return Base64.getEncoder().encodeToString(_bytes);
    }

    public static String decryptAes(String str, String key) throws Exception {
        byte[] _bytes = key.getBytes(StandardCharsets.UTF_8);
        SecretKeySpec _aesKey = new SecretKeySpec(_bytes, "AES");

        Cipher _cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
        _cipher.init(Cipher.DECRYPT_MODE, _aesKey);

        _bytes = Base64.getDecoder().decode(str.getBytes(StandardCharsets.UTF_8));
        _bytes = _cipher.doFinal(_bytes);
        return new String(_bytes);
    }

    public static String encryptRsa(String str, String publicKey) throws Exception {
        byte[] _bytes = Base64.getDecoder().decode(publicKey);
        RSAPublicKey _publicKey = (RSAPublicKey) KeyFactory.getInstance("RSA").generatePublic(new X509EncodedKeySpec(_bytes));

        Cipher _cipher = Cipher.getInstance("RSA");
        _cipher.init(Cipher.ENCRYPT_MODE, _publicKey);

        _bytes = str.getBytes(StandardCharsets.UTF_8);
        _bytes = _cipher.doFinal(_bytes);
        return Base64.getEncoder().encodeToString(_bytes);
    }

    public static String decryptRsa(String str, String privateKey) throws Exception {
        byte[] _bytes = Base64.getDecoder().decode(privateKey);
        RSAPrivateKey _privateKey = (RSAPrivateKey) KeyFactory.getInstance("RSA").generatePrivate(new PKCS8EncodedKeySpec(_bytes));

        Cipher _cipher = Cipher.getInstance("RSA");
        _cipher.init(Cipher.DECRYPT_MODE, _privateKey);

        _bytes = Base64.getDecoder().decode(str.getBytes(StandardCharsets.UTF_8));
        _bytes = _cipher.doFinal(_bytes);
        return new String(_bytes);
    }

}