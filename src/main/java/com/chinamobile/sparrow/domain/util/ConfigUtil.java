package com.chinamobile.sparrow.domain.util;

import org.springframework.beans.factory.config.YamlPropertiesFactoryBean;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;

import java.util.Properties;

// 配置文件工具类
public class ConfigUtil {

    public static String getString(String key) {
        return getString(key, null);
    }

    public static boolean containsKey(String key, Resource resource) {
        Properties _properties = getProperties(resource);
        return _properties.containsKey(key);
    }

    public static String getString(String key, Resource resource) {
        Properties _properties = getProperties(resource);
        return _properties.containsKey(key) ? _properties.getProperty(key) : null;
    }

    protected static Properties getProperties(Resource resource) {
        YamlPropertiesFactoryBean _yaml = new YamlPropertiesFactoryBean();
        _yaml.setResources(resource == null ? new ClassPathResource("application.yml") : resource);
        return _yaml.getObject();
    }

}