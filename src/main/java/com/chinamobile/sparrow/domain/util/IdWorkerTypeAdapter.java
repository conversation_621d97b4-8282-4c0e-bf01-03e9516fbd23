package com.chinamobile.sparrow.domain.util;

import com.google.gson.TypeAdapter;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;

import java.io.IOException;

public class IdWorkerTypeAdapter extends TypeAdapter<Long> {

    @Override
    public void write(JsonWriter jsonWriter, Long aLong) throws IOException {
        jsonWriter.value(String.valueOf(aLong));
    }

    @Override
    public Long read(JsonReader jsonReader) throws IOException {
        return Long.valueOf(jsonReader.nextString());
    }

}