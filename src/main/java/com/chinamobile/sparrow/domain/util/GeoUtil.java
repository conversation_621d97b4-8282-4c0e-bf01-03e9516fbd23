package com.chinamobile.sparrow.domain.util;

public class GeoUtil {

    protected static final double a = 6378245.0;
    protected static final double ee = 0.00669342162296594323;

    /**
     * 84 to 火星坐标系（GCJ-02）
     */
    public static double[] gps84ToGcj02(double lng, double lat) {
        if (isOutland(lng, lat)) {
            return new double[]{lng, lat};
        }

        double _lng = transformLon(lng - 105.0, lat - 35.0);
        double _lat = transformLat(lng - 105.0, lat - 35.0);
        double _radius = lat / 180.0 * Math.PI;

        double _magic = Math.sin(_radius);
        _magic = 1 - ee * _magic * _magic;
        double _magic2 = Math.sqrt(_magic);

        _lng = (_lng * 180.0) / (a / _magic2 * Math.cos(_radius) * Math.PI);
        _lat = (_lat * 180.0) / ((a * (1 - ee)) / (_magic * _magic2) * Math.PI);

        return new double[]{lng + _lng, lat + _lat};
    }

    /**
     * 火星坐标系（GCJ-02） to 84
     */
    public static double[] gcj02ToGps84(double lng, double lat) {
        double[] _location = gps84ToGcj02(lng, lat);

        return new double[]{lng * 2 - _location[0], lat * 2 - _location[1]};
    }

    /**
     * 火星坐标系（GCJ-02） to 百度坐标系（BD-09）
     */
    public static double[] gcj02ToBd09(double lng, double lat) {
        double _z = Math.sqrt(lng * lng + lat * lat) + 0.00002 * Math.sin(lat * 3000.0 * Math.PI / 180.0);
        double _theta = Math.atan2(lat, lng) + 0.000003 * Math.cos(lng * 3000.0 * Math.PI / 180.0);

        return new double[]{_z * Math.cos(_theta) + 0.0065, _z * Math.sin(_theta) + 0.006};
    }

    /**
     * 火星坐标系（GCJ-02） to 百度坐标系（BD-09）
     */
    public static double[] bd09ToGcj02(double lng, double lat) {
        double _x = lng - 0.0065, _y = lat - 0.006;
        double _z = Math.sqrt(_x * _x + _y * _y) - 0.00002 * Math.sin(_y * 3000.0 * Math.PI / 180.0);
        double _theta = Math.atan2(_y, _x) - 0.000003 * Math.cos(_x * 3000.0 * Math.PI / 180.0);

        return new double[]{_z * Math.cos(_theta), _z * Math.sin(_theta)};
    }

    public static double[] gps84ToBd09(double lng, double lat) {
        double[] _gcj02 = gps84ToGcj02(lng, lat);
        return gcj02ToBd09(_gcj02[0], _gcj02[1]);
    }

    public static double[] bd09ToGps84(double lng, double lat) {
        double[] _gcj02 = bd09ToGcj02(lng, lat);
        double[] _gps84 = gcj02ToGps84(_gcj02[0], _gcj02[1]);
        _gps84[0] = format(_gps84[0]);
        _gps84[1] = format(_gps84[1]);

        return _gps84;
    }

    protected static boolean isOutland(double lng, double lat) {
        return (lng < 72.004 || lng > 137.8347) || (lat < 0.8293 || lat > 55.8271);
    }

    protected static double transformLon(double x, double y) {
        double _val = 300.0 + x + 2.0 * y + 0.1 * x * x + 0.1 * x * y + 0.1 * Math.sqrt(Math.abs(x));
        _val += (20.0 * Math.sin(6.0 * x * Math.PI) + 20.0 * Math.sin(2.0 * x * Math.PI)) * 2.0 / 3.0;
        _val += (20.0 * Math.sin(x * Math.PI) + 40.0 * Math.sin(x / 3.0 * Math.PI)) * 2.0 / 3.0;
        _val += (150.0 * Math.sin(x / 12.0 * Math.PI) + 300.0 * Math.sin(x / 30.0 * Math.PI)) * 2.0 / 3.0;

        return _val;
    }

    protected static double transformLat(double x, double y) {
        double _val = -100.0 + 2.0 * x + 3.0 * y + 0.2 * y * y + 0.1 * x * y + 0.2 * Math.sqrt(Math.abs(x));
        _val += (20.0 * Math.sin(6.0 * x * Math.PI) + 20.0 * Math.sin(2.0 * x * Math.PI)) * 2.0 / 3.0;
        _val += (20.0 * Math.sin(y * Math.PI) + 40.0 * Math.sin(y / 3.0 * Math.PI)) * 2.0 / 3.0;
        _val += (160.0 * Math.sin(y / 12.0 * Math.PI) + 320 * Math.sin(y * Math.PI / 30.0)) * 2.0 / 3.0;

        return _val;
    }

    /*
    保留小数点后六位
     */
    protected static double format(double num) {
        String _str = String.format("%.6f", num);
        return Double.parseDouble(_str);
    }

}