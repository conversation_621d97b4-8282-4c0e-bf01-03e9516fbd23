package com.chinamobile.sparrow.domain.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

// 日期工具类
public class DateUtil {

    public static final String DEFAULT_FORMAT = "yyyy-MM-dd HH:mm:ss";

    protected static final Logger LOGGER = LoggerFactory.getLogger(DateUtil.class);

    public static Date from(String str) {
        return from(str, null);
    }

    public static Date from(String str, String format) {
        if (!StringUtils.hasLength(str)) {
            return null;
        }

        try {
            return new SimpleDateFormat(StringUtils.hasLength(format) ? format : DEFAULT_FORMAT).parse(str);
        } catch (ParseException e) {
            LOGGER.error(String.format("转换日期[%s]失败", str), e);

            return null;
        }
    }

    public static long diff(int field, Date start, Date end) {
        long _diff = end.getTime() - start.getTime();

        switch (field) {
            case Calendar.DATE:
                return _diff / (24 * 60 * 60 * 1000);
            case Calendar.HOUR_OF_DAY:
                return _diff / (60 * 60 * 1000);
            case Calendar.MINUTE:
                return _diff / (60 * 1000);
            case Calendar.SECOND:
                return _diff / 1000;
            case Calendar.MILLISECOND:
                return _diff;
            default:
                return -1;
        }
    }

    public static Date getDate(Date date) {
        Calendar _calendar = Calendar.getInstance();
        _calendar.setTime(date);
        _calendar.set(Calendar.HOUR_OF_DAY, 0);
        _calendar.set(Calendar.MINUTE, 0);
        _calendar.set(Calendar.SECOND, 0);
        _calendar.set(Calendar.MILLISECOND, 0);

        return _calendar.getTime();
    }

    public static Date getLastMonday(Date date) {
        Calendar _calendar = Calendar.getInstance();
        _calendar.setTime(date);
        _calendar.add(Calendar.WEEK_OF_MONTH, 0);
        _calendar.set(Calendar.DAY_OF_WEEK, 2);
        _calendar.set(Calendar.HOUR_OF_DAY, 0);
        _calendar.set(Calendar.MINUTE, 0);
        _calendar.set(Calendar.SECOND, 0);
        _calendar.set(Calendar.MILLISECOND, 0);

        return _calendar.getTime();
    }

    public static Date addYears(Date date, int num) {
        return addField(Calendar.YEAR, date, num);
    }

    public static Date addMonths(Date date, int num) {
        return addField(Calendar.MONTH, date, num);
    }

    public static Date addDays(Date date, int num) {
        return addField(Calendar.DATE, date, num);
    }

    public static Date addHours(Date date, int num) {
        return addField(Calendar.HOUR_OF_DAY, date, num);
    }

    public static Date addMinutes(Date date, int num) {
        return addField(Calendar.MINUTE, date, num);
    }

    public static Date addSeconds(Date date, int num) {
        return addField(Calendar.SECOND, date, num);
    }

    public static Date addMilliSeconds(Date date, int num) {
        return addField(Calendar.MILLISECOND, date, num);
    }

    public static Date addField(int field, Date date, int num) {
        Calendar _calender = Calendar.getInstance();
        _calender.setTime(date);
        _calender.add(field, num);

        return _calender.getTime();
    }

    public static String toString(Date date, String format) {
        return new SimpleDateFormat(StringUtils.hasLength(format) ? format : DEFAULT_FORMAT).format(date);
    }

}