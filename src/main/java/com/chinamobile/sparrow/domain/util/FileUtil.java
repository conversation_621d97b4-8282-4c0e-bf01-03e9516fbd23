package com.chinamobile.sparrow.domain.util;

import org.apache.commons.io.FileUtils;
import org.springframework.util.Base64Utils;
import org.springframework.util.FileCopyUtils;
import org.springframework.util.FileSystemUtils;

import java.io.*;

// 文件工具类
public class FileUtil {

    public static String readInputStreamToString(InputStream stream) throws IOException {
        return FileCopyUtils.copyToString(new InputStreamReader(stream));
    }

    public static byte[] readInputStreamToByteArray(InputStream stream) throws IOException {
        return FileCopyUtils.copyToByteArray(stream);
    }

    public static String readByteArrayToBase64String(byte[] bytes) {
        return Base64Utils.encodeToString(bytes);
    }

    public static String readFileToBase64String(File file) throws IOException {
        byte[] _bytes = FileUtils.readFileToByteArray(file);
        return readByteArrayToBase64String(_bytes);
    }

    public static InputStream readBase64StringToInputStream(String base64) {
        byte[] _bytes = Base64Utils.decodeFromString(base64);
        for (int i = 0; i < _bytes.length; i++) {
            if (_bytes[i] < 0) {
                _bytes[i] += 256;
            }
        }

        return new ByteArrayInputStream(_bytes);
    }

    public static boolean delete(File file) {
        return file.isDirectory() ? FileSystemUtils.deleteRecursively(file) : file.delete();
    }

}