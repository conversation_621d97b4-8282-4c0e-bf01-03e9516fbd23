package com.chinamobile.sparrow.domain.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.lang.reflect.Field;
import java.net.JarURLConnection;
import java.net.URL;
import java.net.URLDecoder;
import java.util.*;
import java.util.jar.JarEntry;
import java.util.jar.JarFile;

public class ClassUtil {

    protected static final Logger LOGGER = LoggerFactory.getLogger(ClassUtil.class);

    public static List<Field> getFields(Class<?> tClass) {
        List<Field> _fields = new ArrayList<>();

        for (Class<?> i = tClass; i != Object.class; i = i.getSuperclass()) {
            Collections.addAll(_fields, i.getDeclaredFields());
        }

        return _fields;
    }

    public static Set<Class<?>> getClasses() throws NoSuchFieldException, IllegalAccessException {
        Field _field = ClassLoader.class.getDeclaredField("classes");
        _field.setAccessible(true);

        _field.get(ClassUtil.class.getClassLoader());

        return new HashSet<>((Vector<Class<?>>) _field.get(ClassUtil.class.getClassLoader()));
    }

    public static Set<Class<?>> getClasses(String packageName) throws Exception {
        Set<Class<?>> _classes = new HashSet<>();

        String _packageDirName = packageName.replace('.', '/');

        Enumeration<URL> _resources = Thread.currentThread().getContextClassLoader().getResources(_packageDirName);

        while (_resources.hasMoreElements()) {
            URL _url = _resources.nextElement();

            String _protocol = _url.getProtocol();
            if ("file".equals(_protocol)) {
                String _path = URLDecoder.decode(_url.getFile(), "UTF-8");
                _classes.addAll(getClassesByFile(packageName, _path));
            } else if ("jar".equals(_protocol)) {
                JarFile _jar = ((JarURLConnection) _url.openConnection()).getJarFile();
                _classes.addAll(getClassesByJar(packageName, _jar.entries(), _packageDirName));
            }
        }

        return _classes;
    }

    protected static Set<Class<?>> getClassesByFile(String packageName, String packagePath) {
        Set<Class<?>> _classes = new HashSet<>();

        File _package = new File(packagePath);
        if (!_package.exists() || !_package.isDirectory()) {
            return _classes;
        }

        File[] _files = _package.listFiles(file -> file.isDirectory() || file.getName().endsWith(".class"));

        ClassLoader _classLoader = Thread.currentThread().getContextClassLoader();
        for (File file : _files) {
            if (file.isDirectory()) {
                _classes.addAll(getClassesByFile(packageName + "." + file.getName(), file.getAbsolutePath()));
            } else {
                int _index = file.getName().lastIndexOf(".");
                String _className = file.getName().substring(0, _index);
                _className = String.format("%s.%s", packageName, _className);

                try {
                    _classes.add(_classLoader.loadClass(_className));
                } catch (Throwable e) {
                    LOGGER.debug(String.format("加载%s失败", _className), e);
                }
            }
        }

        return _classes;
    }

    protected static Set<Class<?>> getClassesByJar(String packageName, Enumeration<JarEntry> entries, String packageDirName) {
        Set<Class<?>> _classes = new HashSet<>();

        while (entries.hasMoreElements()) {
            JarEntry _entry = entries.nextElement();
            String _name = _entry.getName();

            if (_name.charAt(0) == '/') {
                _name = _name.substring(1);
            }

            if (_name.startsWith(packageDirName)) {
                int _index = _name.lastIndexOf('/');
                if (_index != -1) {
                    packageName = _name.substring(0, _index).replace('/', '.');
                }

                if (_name.endsWith(".class") && !_entry.isDirectory()) {
                    _index = _name.lastIndexOf(".");
                    String _className = _name.substring(packageName.length() + 1, _index);
                    _className = String.format("%s.%s", packageName, _className);

                    try {
                        _classes.add(Class.forName(_className));
                    } catch (Throwable e) {
                        LOGGER.debug(String.format("加载%s失败", _className), e);
                    }
                }
            }
        }

        return _classes;
    }

}