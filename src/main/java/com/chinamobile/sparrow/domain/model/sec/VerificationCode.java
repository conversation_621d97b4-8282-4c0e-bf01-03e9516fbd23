package com.chinamobile.sparrow.domain.model.sec;

import com.chinamobile.sparrow.domain.model.AbstractEntity;
import com.chinamobile.sparrow.domain.util.IdWorker;
import org.springframework.util.StringUtils;

import javax.persistence.*;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.Date;

@Entity
@Table(name = "sec_verification_codes", indexes = {
        @Index(columnList = "clientId, mp, exp")
})
public class VerificationCode extends AbstractEntity {

    @Id
    @Column(length = 36)
    String id = String.valueOf(IdWorker.getInstance().nextId());

    @Column(length = 36)
    String clientId;

    @Column
    String smsId;

    // 手机号码
    @Column(length = 11, nullable = false)
    @NotBlank
    @Size(max = 11)
    String mp;

    // 验证码
    @Column(length = 16, nullable = false)
    String code;

    // 过期时间
    @Column(nullable = false)
    Date exp;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getClientId() {
        return clientId;
    }

    public void setClientId(String client) {
        this.clientId = StringUtils.trimWhitespace(client);
    }

    public String getSmsId() {
        return smsId;
    }

    public void setSmsId(String smsId) {
        this.smsId = smsId;
    }

    public String getMp() {
        return mp;
    }

    public void setMp(String mp) {
        this.mp = StringUtils.trimWhitespace(mp);
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public Date getExp() {
        return exp;
    }

    public void setExp(Date exp) {
        this.exp = exp;
    }

}