package com.chinamobile.sparrow.domain.model.sec;

import com.chinamobile.sparrow.domain.model.AbstractEntity;
import com.chinamobile.sparrow.domain.util.IdWorker;
import org.springframework.util.StringUtils;

import javax.persistence.*;

@Entity
@Table(name = "sec_permissions", indexes = {
        @Index(columnList = "type")
})
public class Permission extends AbstractEntity {

    @Id
    @Column(length = 36)
    String id = String.valueOf(IdWorker.getInstance().nextId());

    @Column(length = 36)
    String parentId;

    @Column(columnDefinition = "text", nullable = false)
    String name;

    ENUM_TYPE type;

    @Column(columnDefinition = "text")
    String resource;

    @Column(columnDefinition = "text")
    String description;

    int level;

    public Permission() {
    }

    public Permission(String parentId, String name, ENUM_TYPE type, String resource, int level) {
        this.parentId = parentId;
        this.name = StringUtils.hasLength(name) ? StringUtils.trimWhitespace(name) : "";
        this.type = type;
        this.resource = StringUtils.trimWhitespace(resource);
        this.level = level;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getParentId() {
        return parentId;
    }

    public void setParentId(String parentId) {
        this.parentId = parentId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public ENUM_TYPE getType() {
        return type;
    }

    public void setType(ENUM_TYPE type) {
        this.type = type;
    }

    public String getResource() {
        return resource;
    }

    public void setResource(String resource) {
        this.resource = StringUtils.trimWhitespace(resource);
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = StringUtils.trimWhitespace(description);
    }

    public int getLevel() {
        return level;
    }

    public void setLevel(int level) {
        this.level = level;
    }

    public enum ENUM_TYPE {
        PAGE,
        API
    }

}