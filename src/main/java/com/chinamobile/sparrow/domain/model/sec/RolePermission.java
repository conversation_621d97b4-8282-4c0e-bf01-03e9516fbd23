package com.chinamobile.sparrow.domain.model.sec;

import com.chinamobile.sparrow.domain.model.AbstractEntity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name = "sec_role_permissions")
public class RolePermission extends AbstractEntity {

    @Id
    @Column(length = 36)
    String roleId;

    @Id
    @Column(length = 36)
    String permissionId;

    public String getRoleId() {
        return roleId;
    }

    public void setRoleId(String roleId) {
        this.roleId = roleId;
    }

    public String getPermissionId() {
        return permissionId;
    }

    public void setPermissionId(String permissionId) {
        this.permissionId = permissionId;
    }

}