package com.chinamobile.sparrow.domain.model.sys;

import com.chinamobile.sparrow.domain.infra.log.AbstractLog;

import javax.persistence.Entity;
import javax.persistence.Table;

@Entity
@Table(name = "sys_info_logs")
public class InfoLog extends AbstractLog {

    String responseCode;

    public String getResponseCode() {
        return responseCode;
    }

    public void setResponseCode(String responseCode) {
        this.responseCode = responseCode;
    }

}