package com.chinamobile.sparrow.domain.model.sys;

import com.chinamobile.sparrow.domain.model.AbstractEntity;
import com.chinamobile.sparrow.domain.util.IdWorker;
import org.springframework.util.StringUtils;

import javax.persistence.*;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

@Entity
@Table(name = "sys_dictionary", indexes = {
        @Index(columnList = "groupId, name")
})
public class Dictionary extends AbstractEntity {

    @Id
    @Column(length = 36)
    String id = String.valueOf(IdWorker.getInstance().nextId());

    // 分组
    @Column(length = 64, nullable = false)
    @NotBlank
    @Size(max = 64)
    String groupId;

    // 键
    @Column(length = 128, nullable = false)
    @NotBlank
    @Size(max = 128)
    String name;

    // 值
    @Column(columnDefinition = "text", nullable = false)
    @NotBlank
    String val;

    @Column(columnDefinition = "text")
    String description;

    boolean buildIn = false;

    boolean readonly = false;

    Integer seq = Integer.MAX_VALUE;

    boolean isEnabled = true;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getGroupId() {
        return groupId;
    }

    public void setGroupId(String groupId) {
        this.groupId = StringUtils.trimWhitespace(groupId);
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = StringUtils.trimWhitespace(name);
    }

    public String getVal() {
        return val;
    }

    public void setVal(String val) {
        this.val = StringUtils.trimWhitespace(val);
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = StringUtils.trimWhitespace(description);
    }

    public boolean getBuildIn() {
        return buildIn;
    }

    public void setBuildIn(boolean buildIn) {
        this.buildIn = buildIn;
    }

    public boolean getReadonly() {
        return readonly;
    }

    public void setReadonly(boolean readonly) {
        this.readonly = readonly;
    }

    public Integer getSeq() {
        return seq;
    }

    public void setSeq(Integer seq) {
        this.seq = seq;
    }

    public boolean getIsEnabled() {
        return isEnabled;
    }

    public void setIsEnabled(boolean enabled) {
        isEnabled = enabled;
    }

}