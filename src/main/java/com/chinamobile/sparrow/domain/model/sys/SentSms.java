package com.chinamobile.sparrow.domain.model.sys;

import com.chinamobile.sparrow.domain.model.AbstractEntity;
import com.chinamobile.sparrow.domain.util.IdWorker;

import javax.persistence.*;
import java.util.List;

@Entity
@Table(name = "sys_sent_sms")
public class SentSms extends AbstractEntity {

    @Id
    @Column(length = 36)
    String id = String.valueOf(IdWorker.getInstance().nextId());

    // 短信网关序号
    @Column(length = 64)
    String sn;

    // 号码
    @Column(columnDefinition = "text")
    String mpKeys;

    @Transient
    List<String> mps;

    @Column(length = 36)
    String templateId;

    @Column(columnDefinition = "text")
    String params;

    @Column(length = 16)
    String sign;

    ENUM_STATUS status = ENUM_STATUS.TODO;

    @Column(length = 64)
    String responseCode;

    @Column(columnDefinition = "text")
    String responseMessage;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getSn() {
        return sn;
    }

    public void setSn(String sn) {
        this.sn = sn;
    }

    public String getMpKeys() {
        return mpKeys;
    }

    public void setMpKeys(String mpsJSON) {
        this.mpKeys = mpsJSON;
    }

    public List<String> getMps() {
        return mps;
    }

    public void setMps(List<String> mps) {
        this.mps = mps;
    }

    public String getTemplateId() {
        return templateId;
    }

    public void setTemplateId(String templateId) {
        this.templateId = templateId;
    }

    public String getParams() {
        return params;
    }

    public void setParams(String params) {
        this.params = params;
    }

    public String getSign() {
        return sign;
    }

    public void setSign(String sign) {
        this.sign = sign;
    }

    public ENUM_STATUS getStatus() {
        return status;
    }

    public void setStatus(ENUM_STATUS status) {
        this.status = status;
    }

    public String getResponseCode() {
        return responseCode;
    }

    public void setResponseCode(String responseCode) {
        this.responseCode = responseCode;
    }

    public String getResponseMessage() {
        return responseMessage;
    }

    public void setResponseMessage(String responseMeaasge) {
        this.responseMessage = responseMeaasge;
    }

    public enum ENUM_STATUS {

        TODO(0),
        DONE(1),
        FAILED(2),
        CANCELED(3);

        final int value;

        ENUM_STATUS(int value) {
            this.value = value;
        }

        public static ENUM_STATUS valueOf(int value) {
            switch (value) {
                case 0:
                    return TODO;
                case 1:
                    return DONE;
                case 2:
                    return FAILED;
                case 3:
                    return CANCELED;
                default:
                    return null;
            }
        }

        public int value() {
            return this.value;
        }

    }

}