package com.chinamobile.sparrow.domain.model.sec;

import com.chinamobile.sparrow.domain.model.AbstractEntity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name = "sec_user_roles")
public class UserRole extends AbstractEntity {

    @Id
    @Column(length = 36)
    String userId;

    @Id
    @Column(length = 36)
    String roleId;

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getRoleId() {
        return roleId;
    }

    public void setRoleId(String roleId) {
        this.roleId = roleId;
    }

}