package com.chinamobile.sparrow.domain.model.sys;

import com.chinamobile.sparrow.domain.model.AbstractEntity;
import com.chinamobile.sparrow.domain.util.IdWorker;
import org.springframework.util.StringUtils;

import javax.persistence.*;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.Date;

@Entity
@Table(name = "sys_users", indexes = {
        @Index(columnList = "account"),
        @Index(columnList = "name"),
        @Index(columnList = "deptId"),
        @Index(columnList = "mp"),
        @Index(columnList = "yzyId"),
        @Index(columnList = "isEnabled"),
        @Index(columnList = "isOnline"),
        @Index(columnList = "id, account, mp"),
        @Index(columnList = "name, account"),
        @Index(columnList = "name, account, alphabet, mp")
})
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
public class User extends AbstractEntity {

    @Id
    @Column(length = 36)
    protected String id = String.valueOf(IdWorker.getInstance().nextId());

    // 登录帐号
    @Column(length = 64, nullable = false)
    @NotBlank
    @Size(max = 64)
    protected String account;

    // 姓名
    @Column(length = 64, nullable = false)
    @NotBlank
    @Size(max = 64)
    protected String name;

    // 拼音
    @Column(length = 64)
    @Size(max = 64)
    protected String alphabet;

    @Column(length = 36)
    protected String avatarId;

    @Column(columnDefinition = "varchar(36) unique")
    protected String yzyId;

    protected String password;

    @Column(length = 36, nullable = false)
    @Size(max = 36)
    protected String deptId;

    @Transient
    protected String deptName;

    @Transient
    protected String deptCode;

    @Transient
    protected String deptFullName;

    // 手机号码
    @Column(columnDefinition = "varchar(11) unique")
    @Size(max = 11)
    protected String mp;

    // 电子邮箱
    @Column(columnDefinition = "text")
    protected String email;

    // 生日
    protected Date birthday;

    // 地址
    @Column(columnDefinition = "text")
    protected String address;

    // 是否男性
    protected boolean isMale;

    protected Integer seq = Integer.MAX_VALUE;

    protected boolean isLocked = false;

    protected boolean isEnabled = true;

    protected Short loginAttempts;

    protected boolean isOnline;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getAccount() {
        return account;
    }

    public void setAccount(String account) {
        this.account = StringUtils.trimWhitespace(account);
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = StringUtils.trimWhitespace(name);
    }

    public String getAlphabet() {
        return alphabet;
    }

    public void setAlphabet(String alphabet) {
        this.alphabet = StringUtils.trimWhitespace(alphabet);
    }

    public String getAvatarId() {
        return avatarId;
    }

    public void setAvatarId(String avatarId) {
        this.avatarId = avatarId;
    }

    public String getYzyId() {
        return yzyId;
    }

    public void setYzyId(String yzyId) {
        this.yzyId = yzyId;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = StringUtils.trimWhitespace(password);
    }

    public String getDeptId() {
        return deptId;
    }

    public void setDeptId(String deptId) {
        this.deptId = deptId;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public String getDeptCode() {
        return deptCode;
    }

    public void setDeptCode(String deptCode) {
        this.deptCode = deptCode;
    }

    public String getDeptFullName() {
        return deptFullName;
    }

    public void setDeptFullName(String deptFullName) {
        this.deptFullName = deptFullName;
    }

    public String getMp() {
        return mp;
    }

    public void setMp(String mp) {
        this.mp = StringUtils.trimWhitespace(mp);
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = StringUtils.trimWhitespace(email);
    }

    public Date getBirthday() {
        return birthday;
    }

    public void setBirthday(Date birthday) {
        this.birthday = birthday;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = StringUtils.trimWhitespace(address);
    }

    public boolean getIsMale() {
        return isMale;
    }

    public void setIsMale(boolean male) {
        isMale = male;
    }

    public Integer getSeq() {
        return seq;
    }

    public void setSeq(Integer seq) {
        this.seq = seq;
    }

    public boolean getIsLocked() {
        return isLocked;
    }

    public void setIsLocked(boolean locked) {
        isLocked = locked;
    }

    public boolean getIsEnabled() {
        return isEnabled;
    }

    public void setIsEnabled(boolean enabled) {
        isEnabled = enabled;
    }

    public Short getLoginAttempts() {
        return loginAttempts;
    }

    public void setLoginAttempts(Short loginAttempts) {
        this.loginAttempts = loginAttempts;
    }

    public boolean getIsOnline() {
        return isOnline;
    }

    public void setIsOnline(boolean online) {
        isOnline = online;
    }

}