package com.chinamobile.sparrow.domain.model.sys;

import com.chinamobile.sparrow.domain.infra.log.AbstractLog;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

@Entity
@Table(name = "sys_error_logs")
public class ErrorLog extends AbstractLog {

    // 异常
    @Column(columnDefinition = "text")
    String exception;

    @Column(columnDefinition = "text")
    String stackTrace;

    public String getException() {
        return exception;
    }

    public void setException(String exception) {
        this.exception = exception;
    }

    public String getStackTrace() {
        return stackTrace;
    }

    public void setStackTrace(String stackTrace) {
        this.stackTrace = stackTrace;
    }

}