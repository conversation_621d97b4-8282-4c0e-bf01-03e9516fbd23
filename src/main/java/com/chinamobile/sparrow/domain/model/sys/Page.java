package com.chinamobile.sparrow.domain.model.sys;

import com.chinamobile.sparrow.domain.model.AbstractEntity;
import com.chinamobile.sparrow.domain.util.IdWorker;
import org.springframework.util.StringUtils;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name = "sys_pages")
public class Page extends AbstractEntity {

    @Id
    @Column(length = 36)
    String id = String.valueOf(IdWorker.getInstance().nextId());

    @Column(length = 36)
    String parentId;

    // 标题
    @Column(length = 32)
    String title;

    // 图标
    @Column(columnDefinition = "text")
    String icon;

    // 超链接
    @Column(columnDefinition = "text")
    String url;

    Integer level;

    Integer seq;

    @Column(columnDefinition = "text")
    String description;

    boolean requireAuthenticated;

    boolean requireAuthorized;

    public Page() {
    }

    public Page(String title, String parentId, String icon, Integer level, Integer seq, boolean requireAuthenticated, boolean requireAuthorized) {
        this.title = StringUtils.trimWhitespace(title);
        this.parentId = parentId;
        this.icon = StringUtils.trimWhitespace(icon);
        this.level = level;
        this.seq = seq;
        this.requireAuthenticated = requireAuthorized || requireAuthenticated;
        this.requireAuthorized = requireAuthorized;
    }

    public Page(String title, String parentId, String icon, String url, Integer level, Integer seq, boolean requireAuthenticated, boolean requireAuthorized) {
        this(title, parentId, icon, level, seq, requireAuthenticated, requireAuthorized);
        this.url = StringUtils.trimWhitespace(url);
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getParentId() {
        return parentId;
    }

    public void setParentId(String parentId) {
        this.parentId = parentId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = StringUtils.trimWhitespace(title);
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = StringUtils.trimWhitespace(icon);
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = StringUtils.trimWhitespace(url);
    }

    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    public Integer getSeq() {
        return seq;
    }

    public void setSeq(Integer seq) {
        this.seq = seq;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = StringUtils.trimWhitespace(description);
    }

    public boolean getRequireAuthenticated() {
        return requireAuthenticated;
    }

    public void setRequireAuthenticated(boolean requireAuthenticated) {
        this.requireAuthenticated = requireAuthenticated;
    }

    public boolean getRequireAuthorized() {
        return requireAuthorized;
    }

    public void setRequireAuthorized(boolean requireAuthorized) {
        this.requireAuthorized = requireAuthorized;
    }

}