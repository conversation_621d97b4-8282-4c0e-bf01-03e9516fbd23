package com.chinamobile.sparrow.domain.model.sys;

import com.chinamobile.sparrow.domain.model.AbstractEntity;
import com.chinamobile.sparrow.domain.util.IdWorker;
import org.springframework.util.StringUtils;

import javax.persistence.*;
import javax.validation.constraints.NotBlank;

@Entity
@Table(name = "sys_departments", indexes = {
        @Index(columnList = "name"),
        @Index(columnList = "code"),
        @Index(columnList = "level"),
        @Index(columnList = "isEnabled")
})
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
public class Department extends AbstractEntity {

    public static final String CODE_SEPARATOR = ".";
    public static final String NAME_SEPARATOR = "/";

    @Id
    @Column(length = 36)
    protected String id = String.valueOf(IdWorker.getInstance().nextId());

    @Column(nullable = false)
    @NotBlank
    protected String name;

    @Column(columnDefinition = "varchar(250) unique", nullable = false)
    protected String code;

    @Column(columnDefinition = "text", nullable = false)
    protected String fullName;

    // 上级部门
    @Column(length = 36)
    protected String superiorId;

    // 上级部门名称
    @Transient
    protected String superiorName;

    // 级别
    protected int level;

    // 分管领导
    @Column(length = 36)
    protected String supervisorId;

    protected Integer seq = Integer.MAX_VALUE;

    @Transient
    protected boolean hasSubordinates;

    protected boolean isEnabled = true;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = StringUtils.trimWhitespace(name);
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getFullName() {
        return fullName;
    }

    public void setFullName(String fullName) {
        this.fullName = fullName;
    }

    public String getSuperiorId() {
        return superiorId;
    }

    public void setSuperiorId(String superiorId) {
        this.superiorId = superiorId;
    }

    public String getSuperiorName() {
        return superiorName;
    }

    public void setSuperiorName(String superiorName) {
        this.superiorName = superiorName;
    }

    public int getLevel() {
        return level;
    }

    public void setLevel(int level) {
        this.level = level;
    }

    public String getSupervisorId() {
        return supervisorId;
    }

    public void setSupervisorId(String supervisorId) {
        this.supervisorId = supervisorId;
    }

    public Integer getSeq() {
        return seq;
    }

    public void setSeq(Integer seq) {
        this.seq = seq;
    }

    public boolean getHasSubordinates() {
        return hasSubordinates;
    }

    public void setHasSubordinates(boolean hasSubordinates) {
        this.hasSubordinates = hasSubordinates;
    }

    public boolean getIsEnabled() {
        return isEnabled;
    }

    public void setIsEnabled(boolean enabled) {
        isEnabled = enabled;
    }

}