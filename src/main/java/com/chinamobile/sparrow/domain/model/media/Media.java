package com.chinamobile.sparrow.domain.model.media;

import com.chinamobile.sparrow.domain.model.AbstractEntity;
import com.chinamobile.sparrow.domain.util.IdWorker;

import javax.persistence.*;

@Entity
@Table(name = "sys_medias")
public class Media extends AbstractEntity {

    @Id
    @Column(length = 36)
    String id = String.valueOf(IdWorker.getInstance().nextId());

    @Column(columnDefinition = "text", nullable = false)
    String name;

    @Column(length = 16)
    String extension;

    @Column(columnDefinition = "text")
    String path;

    @Column(columnDefinition = "text")
    String content;

    // 临时文件，可以被清理
    boolean isTemporary = true;

    @Transient
    boolean enableEdit;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getExtension() {
        return extension;
    }

    public void setExtension(String extension) {
        this.extension = extension;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public boolean getIsTemporary() {
        return isTemporary;
    }

    public void setIsTemporary(boolean temporary) {
        isTemporary = temporary;
    }

    public boolean getEnableEdit() {
        return enableEdit;
    }

    public void setEnableEdit(boolean enableEdit) {
        this.enableEdit = enableEdit;
    }

}