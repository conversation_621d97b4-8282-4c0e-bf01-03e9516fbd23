package com.chinamobile.sparrow.domain.lang;

// WebSocket消息类
public class WebSocketMessagePayload<T> {

    String subject;
    T data;

    public String getSubject() {
        return subject;
    }

    public void setSubject(String subject) {
        this.subject = subject;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

}