package com.chinamobile.sparrow.domain.lang;

import com.chinamobile.sparrow.domain.infra.code.Result;

public class ResultWarpperRuntimeException extends RuntimeException {

    String code;

    public ResultWarpperRuntimeException() {
    }

    public ResultWarpperRuntimeException(Result<?> result) {
        super(result.message);
        code = result.getCode();
    }

    public Result<?> getResult() {
        Result<?> _result = new Result<>();
        _result.setCode(code);
        _result.message = this.getMessage();
        return _result;
    }

}