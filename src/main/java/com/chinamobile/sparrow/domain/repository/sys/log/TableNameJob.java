package com.chinamobile.sparrow.domain.repository.sys.log;

import com.chinamobile.sparrow.domain.infra.job.JobDetailAndTrigger;
import org.quartz.DisallowConcurrentExecution;
import org.quartz.JobExecutionContext;
import org.quartz.PersistJobDataAfterExecution;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.quartz.QuartzJobBean;

@DisallowConcurrentExecution
@PersistJobDataAfterExecution
@JobDetailAndTrigger(jobName = "cache-log-table-name", jobGroup = "default", triggerName = "cache-log-table-name", triggerGroup = "default", triggerCron = "0 0 * * * ?")
public class TableNameJob extends QuartzJobBean {

    protected final LogRepository logRepository;
    protected final Logger logger;

    public TableNameJob(LogRepository logRepository) {
        this.logRepository = logRepository;
        this.logger = LoggerFactory.getLogger(this.getClass());
    }

    @Override
    protected void executeInternal(JobExecutionContext jobExecutionContext) {
        logger.info("作业开始");

        try {
            logRepository.cacheTableNames();
        } catch (Throwable e) {
            logger.error("作业执行时程序异常", e);
        } finally {
            logger.info("作业结束");
        }
    }

}