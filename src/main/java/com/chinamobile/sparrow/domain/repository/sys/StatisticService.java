package com.chinamobile.sparrow.domain.repository.sys;

import com.chinamobile.sparrow.domain.infra.log.AbstractLog;
import com.chinamobile.sparrow.domain.infra.orm.jing.MySqlFunctions;
import com.chinamobile.sparrow.domain.model.sys.InfoLog;
import com.chinamobile.sparrow.domain.model.sys.User;
import com.chinamobile.sparrow.domain.repository.AbstractJinqRepository;
import com.chinamobile.sparrow.domain.util.DateUtil;
import org.jinq.jpa.JinqJPAStreamProvider;
import org.jinq.orm.stream.JinqStream;
import org.jinq.tuples.Pair;
import org.jinq.tuples.Tuple3;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.persistence.EntityManagerFactory;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

@Transactional(readOnly = true)
public class StatisticService extends AbstractJinqRepository {

    protected final UserRepository userRepository;

    public StatisticService(EntityManagerFactory entityManagerFactory, JinqJPAStreamProvider jinqJPAStreamProvider, UserRepository userRepository) {
        super(entityManagerFactory, jinqJPAStreamProvider);
        this.userRepository = userRepository;
    }

    /**
     * 每日活跃用户数
     *
     * @param begin
     * @param end
     * @param method
     * @param individual
     * @return
     */
    public List<Pair<Date, Long>> dau(Date begin, Date end, String method, boolean individual) {
        if (begin == null) {
            begin = new Date();
        }
        Date _begin = DateUtil.getDate(begin);

        end = DateUtil.addDays(end == null ? begin : end, 1);
        Date _end = DateUtil.getDate(end);

        JinqStream<InfoLog> _query = streamInfoLogs(_begin, _end, method);

        List<Pair<String, Long>> _logs;
        if (individual) {
            _logs = _query.group(i -> MySqlFunctions.dateFormat(i.getStartTime(), "%Y-%m-%d"), (key, stream) -> stream.select(AbstractLog::getActorId).distinct().count())
                    .toList();
        } else {
            _logs = _query.group(i -> MySqlFunctions.dateFormat(i.getStartTime(), "%Y-%m-%d"), (key, stream) -> stream.count())
                    .toList();
        }

        List<Pair<Date, Long>> _counts = new ArrayList<>();
        for (Date i = _begin; i.before(_end); i = DateUtil.addDays(i, 1)) {
            Date _i = i;
            Pair<String, Long> _log = _logs.stream()
                    .filter(j -> Objects.equals(DateUtil.toString(_i, "yyyy-MM-dd"), j.getOne()))
                    .findFirst().orElse(null);
            _counts.add(_log == null ? new Pair<>(i, 0L) : new Pair<>(DateUtil.from(_log.getOne(), "yyyy-MM-dd"), _log.getTwo()));
        }
        return _counts;
    }

    /**
     * 每日活跃率
     *
     * @param begin
     * @param end
     * @param method
     * @return
     */
    public List<Pair<Date, BigDecimal>> dar(Date begin, Date end, String method) {
        // 获取日活跃用户
        List<Pair<Date, Long>> _dau = dau(begin, end, method, true);

        List<User> _users = userRepository.find(null, null, null, null, null, true, null);

        List<Pair<Date, BigDecimal>> _rates = new ArrayList<>();
        for (Pair<Date, Long> i : _dau) {
            Date _date = DateUtil.addDays(i.getOne(), 1);

            long _count = _users.stream()
                    .filter(j -> j.getCreateTime() != null && _date.after(j.getCreateTime()))
                    .count();
            _rates.add(new Pair<>(i.getOne(), _count == 0L ? BigDecimal.ZERO.setScale(4) : (new BigDecimal(i.getTwo())).setScale(4).divide(new BigDecimal(_count), RoundingMode.HALF_UP)));
        }

        return _rates;
    }

    /**
     * 用户留存率
     *
     * @param date
     * @param method
     * @param field
     * @return
     */
    public BigDecimal urr(Date date, String method, int field) {
        if (date == null) {
            date = new Date();
        }

        Date _currentStartDate, _currentEndDate;
        switch (field) {
            case Calendar.YEAR:
                _currentStartDate = new Date(date.getYear(), 1, 1);
                _currentEndDate = DateUtil.addYears(_currentStartDate, 1);
                break;
            case Calendar.MONTH:
                _currentStartDate = new Date(date.getYear(), date.getMonth(), 1);
                _currentEndDate = DateUtil.addMonths(_currentStartDate, 1);
                break;
            default:
                _currentStartDate = DateUtil.getDate(date);
                _currentEndDate = DateUtil.addDays(_currentStartDate, 1);
        }

        Date _lastStartDate = DateUtil.addField(field, _currentStartDate, -1);
        Date _lastEndDate = DateUtil.addField(field, _currentEndDate, -1);
        JinqStream<InfoLog> _query = stream(InfoLog.class)
                .where(i -> i.getActorId() != null && !"".equals(i.getActorId()))
                .where(i -> i.getStartTime().after(_lastStartDate) && !i.getStartTime().after(_lastEndDate));
        if (StringUtils.hasLength(method)) {
            _query = _query.where(i -> method.equals(i.getMethod()));
        }
        List<String> _lastActorIds = _query.select(AbstractLog::getActorId)
                .distinct()
                .toList();

        _query = stream(InfoLog.class)
                .where(i -> i.getActorId() != null && !"".equals(i.getActorId()))
                .where(i -> i.getStartTime().after(_currentStartDate) && !i.getStartTime().after(_currentEndDate));
        if (StringUtils.hasLength(method)) {
            _query = _query.where(i -> method.equals(i.getMethod()));
        }
        List<String> _currentActorIds = _query.select(AbstractLog::getActorId)
                .distinct()
                .toList();

        return CollectionUtils.isEmpty(_lastActorIds) ? BigDecimal.ZERO.setScale(4) : (new BigDecimal(_lastActorIds.stream()
                .filter(i -> !CollectionUtils.isEmpty(_currentActorIds) && _currentActorIds.contains(i))
                .count())).setScale(4).divide(new BigDecimal(_lastActorIds.size()), RoundingMode.HALF_UP);
    }

    /**
     * 活跃分布
     *
     * @param begin
     * @param end
     * @param method
     * @return
     */
    public List<Pair<Integer, Long>> trend(Date begin, Date end, String method) {
        if (begin == null) {
            begin = new Date();
        }
        Date _begin = DateUtil.getDate(begin);

        end = DateUtil.addDays(end == null ? begin : end, 1);
        Date _end = DateUtil.getDate(end);

        List<Pair<Pair<Integer, Integer>, Long>> _query = streamInfoLogs(_begin, _end, method)
                .group(i -> new Pair<>(MySqlFunctions.hour(i.getStartTime()), MySqlFunctions.minute(i.getStartTime()) < 30 ? 0 : 30), (i, stream) -> stream.count())
                .toList();

        List<Pair<Integer, Long>> _counts = new ArrayList<>();
        _query.stream()
                .collect(Collectors.groupingBy(i -> i.getOne().getOne() * 60 + i.getOne().getTwo()))
                .forEach((key, value) -> _counts.add(new Pair<>(key, value.stream()
                        .map(Pair::getTwo)
                        .reduce(0L, Long::sum))));
        return _counts;
    }

    /**
     * 最近最常访问
     *
     * @param count
     * @param date
     * @param field
     * @return
     */
    public List<Tuple3<String, Long, Long>> mru(int count, Date date, int field) {
        if (date == null) {
            date = new Date();
        }

        Date _startDate;
        Date _endDate;
        switch (field) {
            case Calendar.YEAR:
                _startDate = new Date(date.getYear(), 1, 1);
                _endDate = DateUtil.addYears(_startDate, 1);
                break;
            case Calendar.MONTH:
                _startDate = new Date(date.getYear(), date.getMonth(), 1);
                _endDate = DateUtil.addMonths(date, 1);
                break;
            default:
                _startDate = date;
                _endDate = DateUtil.addDays(date, 1);
        }

        JinqStream<Tuple3<String, Long, Long>> _query = stream(InfoLog.class)
                .where(i -> !i.getStartTime().before(_startDate) && i.getStartTime().before(_endDate))
                .group(AbstractLog::getMethod,
                        (key, stream) -> stream.select(AbstractLog::getActorId).distinct().count(),
                        (key, stream) -> stream.count()).sortedDescendingBy(Tuple3::getThree);

        if (count > 0) {
            _query = _query.limit(count);
        }

        return _query.toList();
    }

    protected JinqStream<InfoLog> streamInfoLogs(Date begin, Date end, String method) {
        JinqStream<InfoLog> _query = this.stream(InfoLog.class).where(i -> !i.getStartTime().before(begin) && i.getStartTime().before(end));

        if (StringUtils.hasLength(method)) {
            _query = _query.where(i -> method.equals(i.getMethod()));
        }

        return _query;
    }

}