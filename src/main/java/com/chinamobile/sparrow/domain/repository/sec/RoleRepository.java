package com.chinamobile.sparrow.domain.repository.sec;

import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.lang.PaginatedRecords;
import com.chinamobile.sparrow.domain.lang.Sorter;
import com.chinamobile.sparrow.domain.model.AbstractEntity;
import com.chinamobile.sparrow.domain.model.sec.Permission;
import com.chinamobile.sparrow.domain.model.sec.Role;
import com.chinamobile.sparrow.domain.model.sec.RolePermission;
import com.chinamobile.sparrow.domain.model.sec.UserRole;
import com.chinamobile.sparrow.domain.model.sys.User;
import com.chinamobile.sparrow.domain.repository.AbstractEntityRepository;
import com.chinamobile.sparrow.domain.repository.sys.UserRepository;
import org.jinq.jpa.JinqJPAStreamProvider;
import org.jinq.orm.stream.JinqStream;
import org.jinq.tuples.Pair;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.persistence.EntityManagerFactory;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.function.BiFunction;
import java.util.stream.Collectors;

public class RoleRepository extends AbstractEntityRepository<Role> {

    protected final UserRepository userRepository;
    protected final PermissionRepository permissionRepository;

    public RoleRepository(EntityManagerFactory entityManagerFactory, JinqJPAStreamProvider jinqJPAStreamProvider, UserRepository userRepository, PermissionRepository permissionRepository) {
        super(entityManagerFactory, jinqJPAStreamProvider, Role.class);
        this.userRepository = userRepository;
        this.permissionRepository = permissionRepository;
    }

    @Override
    protected <V extends Comparable<V>> JinqStream<Role> sort(JinqStream<Role> query, BiFunction<JinqStream<Role>, JinqStream.CollectComparable<Role, V>, JinqStream<Role>> compare, String field) {
        switch (field) {
            case "createTime":
                return compare.apply(query, i -> (V) i.getCreateTime());
            case "name":
                return compare.apply(query, i -> (V) i.getName());
            default:
                return query;
        }
    }

    @Transactional(readOnly = true)
    public Result<Role> getById(String id) {
        Result<Role> _record = new Result<>();

        if (!StringUtils.hasLength(id)) {
            _record.setCode(Result.DATABASE_RECORD_NOT_FOUND, new Object[]{Role.class.getSimpleName()});
            return _record;
        }

        _record.data = getCurrentSession().get(Role.class, id);
        if (_record.data == null) {
            _record.setCode(Result.DATABASE_RECORD_NOT_FOUND, new Object[]{Role.class.getSimpleName()});
        }

        return _record;
    }

    @Transactional(readOnly = true)
    public Result<Role> getByName(String name) {
        Result<Role> _record = new Result<>();

        if (!StringUtils.hasLength(name)) {
            _record.setCode(Result.DATABASE_RECORD_NOT_FOUND, new Object[]{Role.class.getSimpleName()});
            return _record;
        }

        _record.data = stream(Role.class).where(i -> name.equals(i.getName()))
                .findFirst().orElse(null);
        if (_record.data == null) {
            _record.setCode(Result.DATABASE_RECORD_NOT_FOUND, new Object[]{Role.class.getSimpleName()});
        }

        return _record;
    }

    @Transactional(readOnly = true)
    public List<Role> search(String name, List<Sorter> sorters) {
        return search(-1, -1, sorters, name).records;
    }

    @Transactional(readOnly = true)
    public PaginatedRecords<Role> search(int count, int index, List<Sorter> sorters, String name) {
        JinqStream<Role> _query = stream(Role.class);

        if (StringUtils.hasLength(name)) {
            _query = _query.where(i -> i.getName().contains(name));
        }

        if (CollectionUtils.isEmpty(sorters)) {
            _query = _query.sortedBy(AbstractEntity::getCreateTime)
                    .sortedDescendingBy(Role::getName);
        } else {
            _query = sort(_query, sorters);
        }

        PaginatedRecords<Role> _page = new PaginatedRecords<>(count, index);
        _page.total = _query.count();

        if (count >= 0 && index >= 0) {
            _query = _query.skip((long) count * index).limit(count);
        }
        _page.records = _query.toList();

        return _page;
    }

    public Result<String> save(Role record, String operatorId) {
        Result<String> _id = new Result<>();

        boolean _alreadyExisted = true;

        Result<Role> _record = ((RoleRepository) proxy).getById(record.getId());
        if (!_record.isOK()) {
            _record.data = new Role();

            _alreadyExisted = false;
        }

        String _identifier = _record.data.getId();
        JinqStream<Role> _query = stream(Role.class).where(i -> _identifier == null || !_identifier.equals(i.getId()));

        String _name = record.getName();
        if (_query.where(i -> _name == null || _name.equals(i.getName()))
                .findFirst().isPresent()) {
            _id.setCode(Result.DATABASE_RECORD_ALREADY_EXIST);
            return _id;
        }

        ((RoleRepository) proxy).copyProperties(record, _record.data, new String[]{"id"});

        Result<Void> _success = _alreadyExisted ? update(_record.data, operatorId) : add(_record.data, operatorId);
        if (!_success.isOK()) {
            return _id.pack(_success);
        }

        _id.data = _record.data.getId();
        return _id;
    }

    public Result<Void> remove(String id) {
        Result<Void> _success = new Result<>();

        Result<Role> _record = ((RoleRepository) proxy).getById(id);
        if (!_record.isOK()) {
            return _success.pack(_record);
        }

        // 删除授权
        for (RolePermission i : stream(RolePermission.class).where(i -> id.equals(i.getRoleId())).toList()) {
            getCurrentSession().remove(i);
        }

        // 删除任命
        for (UserRole i : stream(UserRole.class).where(i -> id.equals(i.getRoleId())).toList()) {
            getCurrentSession().remove(i);
        }

        // 删除角色
        getCurrentSession().remove(_record.data);

        return _success;
    }

    /**
     * 获取角色用户
     *
     * @param id
     * @return
     */
    @Transactional(readOnly = true)
    public List<User> findUsers(String id, String account, String username, String mp, List<Sorter> fields) {
        return findUsers(-1, -1, fields, id, account, username, mp).records;
    }

    /**
     * 获取角色用户
     *
     * @param count
     * @param index
     * @param id
     * @return
     */
    @Transactional(readOnly = true)
    public PaginatedRecords<User> findUsers(int count, int index, List<Sorter> fields, String id, String account, String username, String mp) {
        List<String> _userIds = stream(Role.class).where(i -> i.getId().equals(id))
                .leftOuterJoin((i, session) -> session.stream(UserRole.class), (i, relation) -> i.getId().equals(relation.getRoleId()))
                .where(i -> i.getTwo() != null)
                .select(i -> i.getTwo().getUserId())
                .toList();

        PaginatedRecords<User> _users = userRepository.find(count, index, fields, _userIds, null, StringUtils.hasLength(account) ? Collections.singletonList(account) : null, StringUtils.hasLength(username) ? Collections.singletonList(username) : null, StringUtils.hasLength(mp) ? Collections.singletonList(mp) : null, true);

        // 脱敏
        _users.records = _users.records.stream()
                .map(i -> userRepository.mask(i))
                .collect(Collectors.toList());

        return _users;
    }

    /**
     * 获取角色用户
     *
     * @param name
     * @return
     */
    @Transactional(readOnly = true)
    public List<User> findUsersByName(String name) {
        return findUsersByName(-1, -1, name).records;
    }

    /**
     * 获取角色用户
     *
     * @param count
     * @param index
     * @param name
     * @return
     */
    @Transactional(readOnly = true)
    public PaginatedRecords<User> findUsersByName(int count, int index, String name) {
        List<String> _userIds = stream(Role.class).where(i -> i.getName().equals(name))
                .leftOuterJoin((i, session) -> session.stream(UserRole.class), (i, relation) -> i.getId().equals(relation.getRoleId()))
                .where(i -> i.getTwo() != null)
                .select(i -> i.getTwo().getUserId())
                .toList();

        PaginatedRecords<User> _users = userRepository.find(count, index, null, _userIds, null, null, null, null, true);

        //脱敏
        _users.records = _users.records.stream()
                .map(i -> userRepository.mask(i))
                .collect(Collectors.toList());

        return _users;
    }

    public Result<Void> addUser(String id, String userId, String operatorId) {
        Result<Void> _success = new Result<>();

        Result<Role> _role = ((RoleRepository) proxy).getById(id);
        if (!_role.isOK()) {
            return _success.pack(_role);
        }

        UserRole _relation = stream(UserRole.class)
                .where(i -> i.getRoleId().equals(id) && i.getUserId().equals(userId))
                .findFirst().orElse(null);
        if (_relation == null) {
            _relation = new UserRole();
            _relation.setUserId(userId);
            _relation.setRoleId(id);
            _relation.setCreatorId(operatorId);
            _relation.setCreateTime(new Date());

            getCurrentSession().save(_relation);
        }

        return _success;
    }

    public Result<Void> removeUser(String id, String userId) {
        Result<Void> _success = new Result<>();

        UserRole _relation = stream(UserRole.class)
                .where(i -> i.getRoleId().equals(id) && i.getUserId().equals(userId))
                .findFirst().orElse(null);
        if (_relation == null) {
            _success.setCode(Result.DATABASE_RECORD_NOT_FOUND, new Object[]{UserRole.class.getSimpleName()});
            return _success;
        }

        getCurrentSession().remove(_relation);

        return _success;
    }

    /**
     * 获取用户角色
     *
     * @param userId
     * @return
     */
    @Transactional(readOnly = true)
    public List<Role> findUserRoles(String userId) {
        return userRepository.isEnabled(userId) ? streamUserRoles(userId).toList() : new ArrayList<>();
    }

    /**
     * 判断用户是否属于角色
     *
     * @param userId
     * @param roleName
     * @return
     */
    @Transactional(readOnly = true)
    public boolean isUserInRole(String userId, String roleName) {
        return userRepository.isEnabled(userId) && streamUserRoles(userId).where(i -> i.getName().equals(roleName))
                .findFirst().isPresent();
    }

    /**
     * 读取角色权限
     *
     * @param id
     * @return
     */
    @Transactional(readOnly = true)
    public List<Permission> findPermissions(String id) {
        return stream(Role.class).where(i -> i.getId().equals(id))
                .leftOuterJoin((i, session) -> session.stream(RolePermission.class), (i, relation) -> i.getId().equals(relation.getRoleId()))
                .where(i -> i.getTwo() != null)
                .leftOuterJoin((i, session) -> session.stream(Permission.class), (i, permission) -> permission.getId().equals(i.getTwo().getPermissionId()))
                .where(i -> i.getTwo() != null)
                .select(Pair::getTwo)
                .toList();
    }

    /**
     * 设置角色权限
     *
     * @param id
     * @param permissionIds
     * @param operatorId
     * @return
     */
    public Result<Void> setPermissions(String id, List<String> permissionIds, String operatorId) {
        Result<Void> _success = new Result<>();

        Result<Role> _record = getById(id);
        if (!_record.isOK()) {
            return _success.pack(_record);
        }

        // 删除原权限
        for (RolePermission i : stream(RolePermission.class).where(i -> i.getRoleId().equals(id)).toList()) {
            getCurrentSession().remove(i);
        }

        // 读取权限
        List<Permission> _permissions = permissionRepository.pruning(permissionIds);
        for (Permission i : _permissions) {
            RolePermission _permission = new RolePermission();
            _permission.setRoleId(id);
            _permission.setPermissionId(i.getId());
            _permission.setCreatorId(operatorId);
            _permission.setCreateTime(new Date());

            getCurrentSession().save(_permission);
        }

        return _success;
    }

    protected JinqStream<Role> streamUserRoles(String userId) {
        return stream(UserRole.class).where(i -> i.getUserId().equals(userId))
                .leftOuterJoin((i, session) -> session.stream(Role.class), (i, role) -> role.getId().equals(i.getRoleId()))
                .where(i -> i.getTwo() != null)
                .select(Pair::getTwo);
    }

}