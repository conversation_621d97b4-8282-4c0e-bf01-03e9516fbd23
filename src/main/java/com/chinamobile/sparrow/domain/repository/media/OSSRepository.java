package com.chinamobile.sparrow.domain.repository.media;

import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.model.media.Media;
import com.chinamobile.sparrow.domain.service.S3Adapter;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.jinq.jpa.JinqJPAStreamProvider;
import org.springframework.util.Base64Utils;
import org.springframework.util.FileCopyUtils;
import org.springframework.util.StringUtils;

import javax.persistence.EntityManagerFactory;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public class OSSRepository extends AbstractMediaRepository {

    protected final String env;
    protected final String bucket;
    protected final S3Adapter s3Adapter;

    public OSSRepository(EntityManagerFactory entityManagerFactory, JinqJPAStreamProvider jinqJPAStreamProvider, String extensionsAllow, String extensionsForbid, String env, String bucket, S3Adapter s3Adapter) {
        super(entityManagerFactory, jinqJPAStreamProvider, extensionsAllow, extensionsForbid);
        this.env = env;
        this.bucket = bucket;
        this.s3Adapter = s3Adapter;
    }

    @Override
    public List<ImmutablePair<String, String>> getSubPaths(String path) {
        List<String> _temp = stream(Media.class).where(i -> i.getPath() != null && i.getPath().startsWith(path) && !i.getPath().equals(path))
                .select(Media::getPath)
                .distinct()
                .toList();

        return _temp.stream()
                .map(i -> ImmutablePair.of(i, i.substring(StringUtils.hasLength(path) ? path.length() + 1 : 0).split("/")[0]))
                .distinct()
                .collect(Collectors.toList());
    }

    @Override
    public Result<Media> stage(String bucket, String fileName, String base64, String operatorId) {
        String _bucket = path(bucket);
        return stage(key -> upload(key, _bucket, fileName, base64), _bucket, fileName, operatorId);
    }

    @Override
    public Result<Media> stage(String bucket, File file, String operatorId) {
        String _bucket = path(bucket);
        return stage(key -> upload(key, _bucket, file), _bucket, file.getName(), operatorId);
    }

    @Override
    public Result<Media> add(String bucket, String fileName, String base64, boolean saveFile, String operatorId) {
        String _bucket = path(bucket);
        return add(key -> upload(key, _bucket, fileName, base64), _bucket, fileName, saveFile, false, operatorId);
    }

    @Override
    public Result<Media> add(String bucket, File file, boolean saveFile, String operatorId) {
        String _bucket = path(bucket);
        return add(key -> upload(key, _bucket, file), _bucket, file.getName(), saveFile, false, operatorId);
    }

    protected Result<String> upload(String key, String bucket, String fileName, String base64) {
        byte[] _bytes = Base64Utils.decodeFromString(base64);
        Result<Void> _success = xss(fileName, _bytes);
        return _success.isOK() ? s3Adapter.upload(key, bucket, fileName, _bytes) : new Result<String>().pack(_success);
    }

    protected Result<String> upload(String key, String bucket, File file) {
        try {
            byte[] _bytes = FileCopyUtils.copyToByteArray(file);
            Result<Void> _success = xss(file.getName(), _bytes);
            return _success.isOK() ? s3Adapter.upload(key, bucket, file.getName(), _bytes) : new Result<String>().pack(_success);
        } catch (IOException e) {
            logger.error("保存文件失败", e);

            Result<String> _path = new Result<>();
            _path.setCode(Result.UNKNOWN);
            _path.message = e.getMessage();
            return _path;
        }
    }

    @Override
    public Result<Void> remove(String id, boolean force, String operatorId) {
        return remove((record) -> s3Adapter.remove(record.getPath(), id), id, force, operatorId);
    }

    @Override
    public Result<Void> remove(Media record, boolean force, String operatorId) {
        getCurrentSession().remove(record);

        return StringUtils.hasLength(record.getExtension()) ? s3Adapter.remove(record.getPath(), record.getId()) : new Result<>();
    }

    @Override
    public Result<InputStream> getInputStream(String id, String userId) {
        return ((OSSRepository) proxy).getInputStream(this::getInputStream, id, userId);
    }

    @Override
    public Result<InputStream> getInputStream(Media record) {
        return s3Adapter.download(record.getPath(), record.getId());
    }

    @Override
    public Result<byte[]> getBytes(String id, String userId) throws IOException {
        return getBytes(() -> ((OSSRepository) proxy).getInputStream(id, userId));
    }

    @Override
    public Result<byte[]> getBytes(Media record) throws IOException {
        return getBytes(() -> ((OSSRepository) proxy).getInputStream(record));
    }

    @Override
    public Result<String> getBase64String(String id, String userId) {
        return getBase64String(() -> {
            try {
                return ((OSSRepository) proxy).getBytes(id, userId);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        });
    }

    @Override
    public Result<String> getBase64String(Media record) {
        return getBase64String(() -> {
            try {
                return ((OSSRepository) proxy).getBytes(record);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        });
    }

    @Override
    public Result<byte[]> getPart(String id, Long start, Long end, String userId) {
        return getPart(record -> ((OSSRepository) proxy).getPart(record, start, end), id, userId);
    }

    @Override
    public Result<byte[]> getPart(Media record, Long start, Long end) {
        return s3Adapter.downloadPart(record.getPath(), record.getId(), start, end);
    }

    @Override
    public Result<Long> getContentLength(String id, String userId) {
        return ((OSSRepository) proxy).getContentLength(this::getContentLength, id, userId);
    }

    @Override
    public Result<Long> getContentLength(Media record) {
        return s3Adapter.getContentLength(record.getPath(), record.getId());
    }

    @Override
    public String path(String bucket) {
        return Stream.of(env, StringUtils.hasLength(bucket) ? bucket : this.bucket)
                .filter(StringUtils::hasLength)
                .collect(Collectors.joining("/"));
    }

}