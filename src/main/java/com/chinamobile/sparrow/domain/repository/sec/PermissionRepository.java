package com.chinamobile.sparrow.domain.repository.sec;

import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.model.AbstractEntity;
import com.chinamobile.sparrow.domain.model.sec.Permission;
import com.chinamobile.sparrow.domain.model.sec.Role;
import com.chinamobile.sparrow.domain.model.sec.RolePermission;
import com.chinamobile.sparrow.domain.model.sec.UserRole;
import com.chinamobile.sparrow.domain.repository.AbstractEntityRepository;
import com.chinamobile.sparrow.domain.repository.sys.PageRepository;
import com.chinamobile.sparrow.domain.repository.sys.UserRepository;
import com.chinamobile.sparrow.domain.util.ClassUtil;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jinq.jpa.JinqJPAStreamProvider;
import org.jinq.orm.stream.JinqStream;
import org.jinq.tuples.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.persistence.EntityManagerFactory;
import java.util.*;
import java.util.stream.Collectors;

public class PermissionRepository extends AbstractEntityRepository<Permission> {

    protected final UserRepository userRepository;
    protected final PageRepository pageRepository;
    protected final Logger logger;

    public PermissionRepository(EntityManagerFactory entityManagerFactory, JinqJPAStreamProvider jinqJPAStreamProvider, UserRepository userRepository, PageRepository pageRepository) {
        super(entityManagerFactory, jinqJPAStreamProvider, Permission.class);
        this.userRepository = userRepository;
        this.pageRepository = pageRepository;
        this.logger = LoggerFactory.getLogger(this.getClass());
    }

    @Transactional(readOnly = true)
    public Result<Permission> get(String id) {
        Result<Permission> _record = new Result<>();

        if (!StringUtils.hasLength(id)) {
            _record.setCode(Result.DATABASE_RECORD_NOT_FOUND, new Object[]{Permission.class.getSimpleName()});
            return _record;
        }

        _record.data = getCurrentSession().get(Permission.class, id);
        if (_record.data == null) {
            _record.setCode(Result.DATABASE_RECORD_NOT_FOUND, new Object[]{Permission.class.getSimpleName()});
        }

        return _record;
    }

    @Transactional(readOnly = true)
    public List<Permission> find(Permission.ENUM_TYPE type) {
        JinqStream<Permission> _query = stream(Permission.class);

        if (type != null) {
            _query = _query.where(i -> type == i.getType());
        }

        return _query.sortedBy(AbstractEntity::getCreateTime)
                .sortedBy(Permission::getName)
                .toList();
    }

    public Result<Void> remove(String id) {
        Result<Void> _success = new Result<>();

        Result<Permission> _record = ((PermissionRepository) proxy).get(id);
        if (!_record.isOK()) {
            return _success.pack(_record);
        }

        getCurrentSession().remove(_record.data);

        return _success;
    }

    public void renew(String operatorId) throws NoSuchFieldException, IllegalAccessException {
        Map<String, String[]> _pagePermissionMappings = pageRepository.readPagePermissionMappings();

        // 从页面生成映射
        HashMap<String, String[]> _pathMappings = new HashMap<>(_pagePermissionMappings);

        // 从Controller合并映射
        readControllerPathMappings().forEach((controller, path) -> Arrays.stream(controller.getMethods())
                .filter(i -> i.getAnnotation(RequiresPermissions.class) != null)
                // 遍历要求鉴权的方法
                .forEach(i -> {
                    // 首先从RequestMapping读取path
                    String _path = Optional.ofNullable(i.getAnnotation(RequestMapping.class))
                            .map(k -> k.value().length == 0 ? "" : k.value()[0])
                            .orElse(null);

                    // 其次从GetMapping读取path
                    if (_path == null) {
                        _path = Optional.ofNullable(i.getAnnotation(GetMapping.class))
                                .map(k -> k.value().length == 0 ? "" : k.value()[0])
                                .orElse(null);
                    }

                    // 再次从PostMapping读取path
                    if (_path == null) {
                        _path = Optional.ofNullable(i.getAnnotation(PostMapping.class))
                                .map(k -> k.value().length == 0 ? "" : k.value()[0])
                                .orElse(null);
                    }

                    if (_path != null) {
                        _path = path + _path;
                        _pathMappings.put(StringUtils.hasLength(_path) ? _path : "/", i.getAnnotation(RequiresPermissions.class).value());
                    }
                }));

        Map<String, Permission> _itemsMap = new LinkedHashMap<>();

        // 读取权限
        List<Permission> _records = ((PermissionRepository) proxy).find(null);

        Map<String, Set<String>> _permissionPageMappings = new HashMap<>();
        _pathMappings.forEach((key, value) -> {
            if (value == null) {
                return;
            }

            for (String i : value) {
                if (_permissionPageMappings.containsKey(i)) {
                    _permissionPageMappings.get(i).add(key);
                } else {
                    _permissionPageMappings.put(i, new LinkedHashSet<String>() {{
                        add(key);
                    }});
                }
            }
        });

        _permissionPageMappings.forEach((key, value) -> {
            String _parentId = null;

            // 解析一级权限
            String[] _permissions = key.split(":", 3);
            if (_permissions.length > 0) {
                String _name = String.format("%s:*", _permissions[0]);
                Permission _record = _itemsMap.get(_name);
                if (_record == null) {
                    _record = _records.stream()
                            .filter(i -> Objects.equals(i.getName(), _name))
                            .findFirst().orElse(new Permission(null, _name, null, null, 1));

                    _itemsMap.put(_record.getName(), _record);
                }

                _parentId = _record.getId();
            }

            // 解析二级权限
            if (_permissions.length > 1) {
                String _name = String.format("%s:%s:*", _permissions[0], _permissions[1]);
                Permission _record = _itemsMap.get(_name);
                if (_record == null) {
                    _record = _records.stream()
                            .filter(i -> Objects.equals(i.getName(), _name))
                            .findFirst().orElse(new Permission(_parentId, _name, null, null, 2));

                    _itemsMap.put(_record.getName(), _record);
                }

                _parentId = _record.getId();
            }

            // 解析三级权限
            Permission _record = _itemsMap.get(key);
            if (_record == null) {
                _record = _records.stream()
                        .filter(i -> Objects.equals(i.getName(), key))
                        .findFirst()
                        .orElse(null);
                if (_record == null) {
                    String _resource = value.stream()
                            .findFirst().orElse(null);
                    Permission.ENUM_TYPE _type = StringUtils.hasLength(_resource) ? (_pagePermissionMappings.containsKey(_resource) ? Permission.ENUM_TYPE.PAGE : Permission.ENUM_TYPE.API) : null;

                    _record = new Permission(_parentId, key, _type, _resource, 3);
                }

                _itemsMap.put(_record.getName(), _record);
            }
        });

        // 分类
        List<Permission> _itemsToAdd = new ArrayList<>(), _itemsToRemain = new ArrayList<>();
        for (Permission i : _itemsMap.values()) {
            String _name = i.getName();
            Permission _record = _records.stream()
                    .filter(j -> Objects.equals(j.getName(), _name))
                    .findFirst().orElse(null);
            if (_record == null) {
                _itemsToAdd.add(i);
            } else {
                _itemsToRemain.add(_record);
            }
        }

        // 删除无效权限
        _records.removeAll(_itemsToRemain);
        for (Permission i : _records) {
            remove(i.getId());
        }

        // 新增权限
        for (Permission i : _itemsToAdd) {
            add(i, operatorId);
        }
    }

    /**
     * 剪枝
     *
     * @param permissionIds
     * @return
     */
    @Transactional(readOnly = true)
    public List<Permission> pruning(List<String> permissionIds) {
        if (CollectionUtils.isEmpty(permissionIds)) {
            return new ArrayList<>();
        }

        List<Permission> _records = find(null).stream()
                .filter(i -> permissionIds.contains(i.getId()))
                .sorted(Comparator.comparing(Permission::getLevel, Comparator.nullsFirst(Comparator.naturalOrder())))
                .collect(Collectors.toList());
        int _root = _records.stream()
                .min(Comparator.comparing(Permission::getLevel, Comparator.nullsFirst(Comparator.naturalOrder())))
                .map(Permission::getLevel).orElse(1);
        int _leaf = _records.stream()
                .max(Comparator.comparing(Permission::getLevel, Comparator.nullsFirst(Comparator.naturalOrder())))
                .map(Permission::getLevel).orElse(_root);

        List<Permission> _pruning = new ArrayList<>();
        for (int i = _leaf; i >= _root; i--) {
            int _i = i;
            List<Permission> _permissions = _records.stream()
                    .filter(j -> _i == j.getLevel())
                    .collect(Collectors.toList());
            for (Permission j : _permissions) {
                if (_records.stream().noneMatch(k -> Objects.equals(j.getParentId(), k.getId()))) {
                    _pruning.add(j);
                }
            }
        }

        return _pruning;
    }

    /**
     * 读取用户权限
     *
     * @param userId
     * @return
     */
    @Transactional(readOnly = true)
    public List<Permission> findUserPermissions(String userId) {
        return userRepository.isEnabled(userId) ? stream(UserRole.class).where(i -> i.getUserId().equals(userId))
                .leftOuterJoin((i, session) -> session.stream(Role.class), (i, role) -> role.getId().equals(i.getRoleId()))
                .where(i -> i.getTwo() != null)
                .leftOuterJoin((i, session) -> session.stream(RolePermission.class), (i, relation) -> relation.getRoleId().equals(i.getTwo().getId()))
                .where(i -> i.getTwo() != null)
                .leftOuterJoin((i, session) -> session.stream(Permission.class), (i, permission) -> permission.getId().equals(i.getTwo().getPermissionId()))
                .where(i -> i.getTwo() != null)
                .select(Pair::getTwo)
                .toList() : new ArrayList<>();
    }

    public Map<Class<?>, String> readControllerPathMappings() throws NoSuchFieldException, IllegalAccessException {
        Map<Class<?>, String> _map = new HashMap<>();

        ClassUtil.getClasses().forEach(i -> {
            try {
                if (i.getAnnotation(Controller.class) == null && i.getAnnotation(RestController.class) == null) {
                    return;
                }

                String _path = Optional.ofNullable(i.getAnnotation(RequestMapping.class))
                        .map(j -> j.value().length == 0 ? "" : j.value()[0])
                        .orElse("");
                _map.put(i, StringUtils.hasLength(_path) ? "/" + _path : _path);
            } catch (Throwable e) {
                logger.debug(String.format("读取Controller/RequestMapping注解类，无法解析%s类", i.getName()), e);
            }
        });

        return _map;
    }

}