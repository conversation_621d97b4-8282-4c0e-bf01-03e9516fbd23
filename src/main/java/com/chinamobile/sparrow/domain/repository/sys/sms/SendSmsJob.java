package com.chinamobile.sparrow.domain.repository.sys.sms;

import com.chinamobile.sparrow.domain.infra.job.JobDetailAndTrigger;
import org.apache.commons.lang3.tuple.Pair;
import org.quartz.DisallowConcurrentExecution;
import org.quartz.JobExecutionContext;
import org.quartz.PersistJobDataAfterExecution;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.quartz.QuartzJobBean;

@DisallowConcurrentExecution
@PersistJobDataAfterExecution
@JobDetailAndTrigger(jobName = "sms-send", jobGroup = "default", triggerName = "sms-send", triggerGroup = "default", triggerCron = "0/5 * * * * ?", triggerOnStart = false)
public class SendSmsJob extends QuartzJobBean {

    protected final SentSmsRepository sentSmsRepository;
    protected final Logger logger;

    public SendSmsJob(SentSmsRepository sentSmsRepository) {
        this.sentSmsRepository = sentSmsRepository;
        this.logger = LoggerFactory.getLogger(this.getClass());
    }

    @Override
    protected void executeInternal(JobExecutionContext jobExecutionContext) {
        logger.info("作业开始");

        try {
            Pair<Integer, Integer> _count = sentSmsRepository.sendSms();
            logger.info(String.format("已提交%s项任务，其中%s项成功，%s项失败", _count.getLeft() + _count.getRight(), _count.getLeft(), _count.getRight()));
        } catch (Throwable e) {
            logger.info("作业执行时程序异常", e);
        } finally {
            logger.info("作业结束");
        }
    }

}