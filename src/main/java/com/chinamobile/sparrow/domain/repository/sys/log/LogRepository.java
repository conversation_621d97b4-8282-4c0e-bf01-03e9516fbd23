package com.chinamobile.sparrow.domain.repository.sys.log;

import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.infra.log.AbstractLog;
import com.chinamobile.sparrow.domain.lang.PaginatedRecords;
import com.chinamobile.sparrow.domain.lang.Sorter;
import com.chinamobile.sparrow.domain.model.sys.InfoLog;
import com.chinamobile.sparrow.domain.model.sys.User;
import com.chinamobile.sparrow.domain.repository.AbstractJinqRepository;
import com.chinamobile.sparrow.domain.repository.sys.UserRepository;
import com.chinamobile.sparrow.domain.util.DateUtil;
import org.jinq.jpa.JinqJPAStreamProvider;
import org.jinq.orm.stream.JinqStream;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.persistence.EntityManagerFactory;
import java.sql.ResultSet;
import java.util.*;
import java.util.stream.Collectors;

@Transactional(readOnly = true)
public class LogRepository extends AbstractJinqRepository {

    public static Set<String> tableNames = new HashSet<>();

    protected final String INFO_LOG_PREFIX = "sys_info_logs_";
    protected final String ERROR_LOG_PREFIX = "sys_error_logs_";

    protected final UserRepository userRepository;

    public LogRepository(EntityManagerFactory entityManagerFactory, JinqJPAStreamProvider jinqJPAStreamProvider, UserRepository userRepository) {
        super(entityManagerFactory, jinqJPAStreamProvider);
        this.userRepository = userRepository;
    }

    public <T extends AbstractLog> PaginatedRecords<T> search(Class<T> type, int count, int index, List<Sorter> sorters, List<String> methods, String code, String account, String ip, String realmType, Date startTime, Date endTime) {
        JinqStream<T> _query = stream(type);

        if (!CollectionUtils.isEmpty(methods)) {
            _query = _query.where(i -> methods.contains(i.getMethod()));
        }

        if (type == InfoLog.class) {
            if (StringUtils.hasLength(code)) {
                String _code = code.toUpperCase();
                _query = (JinqStream<T>) ((JinqStream<InfoLog>) _query).where(i -> _code.equals(i.getResponseCode()));
            }
        }

        if (StringUtils.hasLength(account)) {
            Result<User> _user = userRepository.getBriefByIdOrAccountOrMp(account, null);
            if (_user.isOK()) {
                String _userId = _user.data.getId();
                _query = _query.where(i -> i.getActorId().equals(_userId));
            } else {
                _query = _query.where(i -> false);
            }
        }

        if (StringUtils.hasLength(ip)) {
            _query = _query.where(i -> i.getIp() != null && i.getIp().contains(ip));
        }

        if (StringUtils.hasLength(realmType)) {
            _query = _query.where(i -> realmType.equals(i.getRealmType()));
        }

        Date _startTime = startTime == null ? DateUtil.getDate(new Date()) : startTime;
        _query = _query.where(i -> !i.getStartTime().before(_startTime));

        if (endTime != null) {
            _query = _query.where(i -> !i.getStartTime().after(endTime));
        }

        if (CollectionUtils.isEmpty(sorters)) {
            _query = _query.sortedDescendingBy(AbstractLog::getStartTime);
        } else {
            Collections.reverse(sorters);

            for (Sorter i : sorters) {
                switch (i.getField()) {
                    case "startTime":
                        _query = i.getAsc() ? _query.sortedBy(AbstractLog::getStartTime) : _query.sortedDescendingBy(AbstractLog::getStartTime);
                        break;
                    case "endTime":
                        _query = i.getAsc() ? _query.sortedBy(AbstractLog::getEndTime) : _query.sortedDescendingBy(AbstractLog::getEndTime);
                        break;
                }
            }
        }

        PaginatedRecords<T> _page = new PaginatedRecords<>(count, index);
        _page.total = _query.count();

        if (count >= 0 && index >= 0) {
            _query = _query.skip((long) count * index).limit(count);
        }
        _page.records = _query.toList();

        List<String> _userIds = _page.records.stream()
                .map(AbstractLog::getActorId)
                .collect(Collectors.toList());
        List<User> _users = CollectionUtils.isEmpty(_userIds) ? new ArrayList<>() : userRepository.findBrief(_userIds, null, null, null, null, null, null);

        for (T i : _page.records) {
            User _user = _users.stream()
                    .filter(j -> Objects.equals(i.getActorId(), j.getId()))
                    .findFirst().orElse(null);
            if (_user != null) {
                i.setAccount(_user.getAccount());
                i.setActorName(_user.getName());
            }

            if (i.getStartTime() != null && i.getEndTime() != null) {
                i.setPeriod(DateUtil.diff(Calendar.MILLISECOND, i.getStartTime(), i.getEndTime()));
            }
        }

        return _page;
    }

    public void cacheTableNames() {
        getCurrentSession().doReturningWork(connection -> {
            tableNames.clear();

            ResultSet _tables = connection.getMetaData().getTables(connection.getCatalog(), null, null, new String[]{"TABLE"});
            while (_tables.next()) {
                String _tableName = _tables.getString("TABLE_NAME");
                if (_tableName.startsWith(INFO_LOG_PREFIX) || _tableName.startsWith(ERROR_LOG_PREFIX)) {
                    tableNames.add(_tableName);
                }
            }

            return null;
        });
    }

}