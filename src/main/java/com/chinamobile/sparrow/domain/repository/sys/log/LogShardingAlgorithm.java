package com.chinamobile.sparrow.domain.repository.sys.log;

import com.chinamobile.sparrow.domain.util.DateUtil;
import org.apache.shardingsphere.sharding.api.sharding.standard.PreciseShardingValue;
import org.apache.shardingsphere.sharding.api.sharding.standard.RangeShardingValue;
import org.apache.shardingsphere.sharding.api.sharding.standard.StandardShardingAlgorithm;

import java.util.*;

public class LogShardingAlgorithm implements StandardShardingAlgorithm<Date> {

    @Override
    public void init(Properties properties) {
    }

    @Override
    public String doSharding(Collection<String> collection, PreciseShardingValue<Date> preciseShardingValue) {
        String _val = DateUtil.toString(preciseShardingValue.getValue(), "yyyyMM");

        return String.format("%s_%s", preciseShardingValue.getLogicTableName(), _val);
    }

    @Override
    public Collection<String> doSharding(Collection<String> collection, RangeShardingValue<Date> rangeShardingValue) {
        List<String> _tableNames = new ArrayList<>();

        Date _begin = rangeShardingValue.getValueRange().hasLowerBound() ? DateUtil.from(String.valueOf(rangeShardingValue.getValueRange().lowerEndpoint()), "yyyy-MM-dd") : null;
        if (_begin != null) {
            _begin = new Date(_begin.getYear(), _begin.getMonth(), 1);
        }

        Date _end = rangeShardingValue.getValueRange().hasUpperBound() ? DateUtil.from(String.valueOf(rangeShardingValue.getValueRange().upperEndpoint()), "yyyy-MM-dd") : null;
        if (_end != null) {
            _end = new Date(_end.getYear(), _end.getMonth(), 1);
        }

        for (String i : LogRepository.tableNames) {
            if (!i.startsWith(rangeShardingValue.getLogicTableName())) {
                continue;
            }

            String _month = i.split("_")[3];

            if (_begin != null && DateUtil.from(_month, "yyyyMM").before(_begin)) {
                continue;
            }

            if (_end != null && DateUtil.from(_month, "yyyyMM").after(_end)) {
                continue;
            }

            _tableNames.add(i);
        }

        return _tableNames;
    }

    @Override
    public Properties getProps() {
        return null;
    }

    @Override
    public String getType() {
        return "SHARDING_LOGS_BY_MONTHS";
    }

    @Override
    public Collection<String> getTypeAliases() {
        return StandardShardingAlgorithm.super.getTypeAliases();
    }

}