package com.chinamobile.sparrow.domain.repository;

import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.lang.Sorter;
import com.chinamobile.sparrow.domain.model.AbstractEntity;
import com.chinamobile.sparrow.domain.model.sys.User;
import org.jinq.jpa.JinqJPAStreamProvider;
import org.jinq.orm.stream.JinqStream;
import org.springframework.beans.BeanUtils;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.persistence.EntityManagerFactory;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.function.BiFunction;
import java.util.stream.Collectors;

@Transactional(propagation = Propagation.SUPPORTS)
public abstract class AbstractEntityRepository<T extends AbstractEntity> extends AbstractJinqRepository {

    protected final Class<T> tClass;

    protected AbstractEntityRepository(EntityManagerFactory entityManagerFactory, JinqJPAStreamProvider jinqJPAStreamProvider, Class<T> tClass) {
        super(entityManagerFactory, jinqJPAStreamProvider);
        this.tClass = tClass;
    }

    /**
     * 新增
     *
     * @param record
     * @param operatorId
     * @return
     */
    public Result<Void> add(T record, String operatorId) {
        record.setCreatorId(operatorId);
        record.setCreateTime(new Date());

        getCurrentSession().save(record);

        return new Result<>();
    }

    /**
     * 更新
     *
     * @param record
     * @param operatorId
     * @return
     */
    public Result<Void> update(T record, String operatorId) {
        record.setMaintainerId(operatorId);
        record.setMaintainTime(new Date());

        getCurrentSession().update(record);

        return new Result<>();
    }

    @Transactional(readOnly = true)
    public void copyProperties(T source, T target, String[] ignoreProperties) {
        if (ignoreProperties == null) {
            ignoreProperties = new String[0];
        }

        String[] _ignoreProperties = new String[ignoreProperties.length + 4];

        System.arraycopy(ignoreProperties, 0, _ignoreProperties, 0, ignoreProperties.length);

        String[] _fields = new String[]{"creatorId", "createTime", "maintainerId", "maintainTime"};
        System.arraycopy(_fields, 0, _ignoreProperties, ignoreProperties.length, _fields.length);

        BeanUtils.copyProperties(source, target, _ignoreProperties);
    }

    /**
     * 排序
     *
     * @param query
     * @param sorters
     * @return
     */
    protected JinqStream<T> sort(JinqStream<T> query, List<Sorter> sorters) {
        if (CollectionUtils.isEmpty(sorters)) {
            return query;
        }

        Collections.reverse(sorters);
        for (Sorter i : sorters) {
            query = sort(query, i.getAsc() ? JinqStream::sortedBy : JinqStream::sortedDescendingBy, i.getField());
        }

        return query;
    }

    protected <V extends Comparable<V>> JinqStream<T> sort(JinqStream<T> query, BiFunction<JinqStream<T>, JinqStream.CollectComparable<T, V>, JinqStream<T>> compare, String field) {
        return query;
    }

    /**
     * 数据脱敏
     *
     * @param record
     * @return
     */
    @Transactional(readOnly = true)
    public T mask(T record) throws InstantiationException, IllegalAccessException {
        T _record = tClass.newInstance();
        copyProperties(record, _record, null);

        return _record;
    }

    public void parseUsers(List<T> records) {
        if (CollectionUtils.isEmpty(records)) {
            return;
        }

        List<String> _userIds = records.stream()
                .map(AbstractEntity::getCreatorId)
                .filter(StringUtils::hasLength)
                .collect(Collectors.toList());

        List<String> _maintainerIds = records.stream()
                .map(AbstractEntity::getMaintainerId)
                .filter(StringUtils::hasLength)
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(_maintainerIds)) {
            _userIds.addAll(_maintainerIds);
        }

        if (CollectionUtils.isEmpty(_userIds)) {
            return;
        }

        List<User> _users = stream(User.class).where(i -> _userIds.contains(i.getId())).toList();

        for (T i : records) {
            User _user = _users.stream()
                    .filter(j -> Objects.equals(i.getCreatorId(), j.getId()))
                    .findFirst().orElse(null);
            if (_user != null) {
                i.setCreatorName(_user.getName());
            }

            _user = _users.stream()
                    .filter(j -> Objects.equals(i.getMaintainerId(), j.getId()))
                    .findFirst().orElse(null);
            if (_user != null) {
                i.setMaintainerName(_user.getName());
            }
        }
    }

}