package com.chinamobile.sparrow.domain.repository.sys.sms;

import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.lang.PaginatedRecords;
import com.chinamobile.sparrow.domain.lang.Sorter;
import com.chinamobile.sparrow.domain.model.AbstractEntity;
import com.chinamobile.sparrow.domain.model.sys.SentSms;
import com.chinamobile.sparrow.domain.repository.AbstractEntityRepository;
import com.chinamobile.sparrow.domain.service.MASFacade;
import com.chinamobile.sparrow.domain.util.ConverterUtil;
import com.chinamobile.sparrow.domain.util.DateUtil;
import com.google.common.reflect.TypeToken;
import org.apache.commons.lang3.tuple.Pair;
import org.jinq.jpa.JinqJPAStreamProvider;
import org.jinq.orm.stream.JinqStream;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.persistence.EntityManagerFactory;
import java.util.Date;
import java.util.List;
import java.util.function.BiFunction;

public class SentSmsRepository extends AbstractEntityRepository<SentSms> {

    protected final MASFacade masFacade;

    public SentSmsRepository(EntityManagerFactory entityManagerFactory, JinqJPAStreamProvider jinqJPAStreamProvider, MASFacade masFacade) {
        super(entityManagerFactory, jinqJPAStreamProvider, SentSms.class);
        this.masFacade = masFacade;
    }

    @Override
    protected <V extends Comparable<V>> JinqStream<SentSms> sort(JinqStream<SentSms> query, BiFunction<JinqStream<SentSms>, JinqStream.CollectComparable<SentSms, V>, JinqStream<SentSms>> compare, String field) {
        switch (field) {
            case "createTime":
                return compare.apply(query, i -> (V) i.getCreateTime());
            case "mpKeys":
                return compare.apply(query, i -> (V) i.getMpKeys());
            case "status":
                return compare.apply(query, i -> (V) i.getStatus());
            case "templateId":
                return compare.apply(query, i -> (V) i.getTemplateId());
            default:
                return query;
        }
    }

    public Result<String> inbox(List<String> mps, String templateId, List<String> params, String sign, String operatorId) {
        Result<String> _id = new Result<>();

        SentSms _record = new SentSms();
        _record.setMpKeys(CollectionUtils.isEmpty(mps) ? null : ConverterUtil.toJson(mps));
        _record.setTemplateId(templateId);
        _record.setParams(CollectionUtils.isEmpty(params) ? null : ConverterUtil.toJson(params));
        _record.setSign(sign);

        Result<Void> _success = add(_record, operatorId);
        if (!_success.isOK()) {
            return _id.pack(_success);
        }

        _id.data = _record.getId();
        return _id;
    }

    public Result<String> inbox(List<String> mps, String content, String sign, String operatorId) {
        Result<String> _id = new Result<>();

        SentSms _record = new SentSms();
        _record.setMpKeys(CollectionUtils.isEmpty(mps) ? null : ConverterUtil.toJson(mps));
        _record.setParams(content);
        _record.setSign(sign);

        Result<Void> _success = add(_record, operatorId);
        if (!_success.isOK()) {
            return _id.pack(_success);
        }

        _id.data = _record.getId();
        return _id;
    }

    @Transactional(readOnly = true)
    public PaginatedRecords<SentSms> search(int count, int index, List<Sorter> sorters, String mp, String param, SentSms.ENUM_STATUS status, Date beginTime, Date endTime) {
        JinqStream<SentSms> _query = stream(SentSms.class);

        if (StringUtils.hasLength(mp)) {
            _query = _query.where(i -> i.getMpKeys() != null && i.getMpKeys().contains(mp));
        }

        if (StringUtils.hasLength(param)) {
            _query = _query.where(i -> i.getParams() != null && i.getParams().contains(param));
        }

        if (status != null) {
            _query = _query.where(i -> status == i.getStatus());
        }

        if (beginTime != null) {
            _query = _query.where(i -> !beginTime.after(i.getCreateTime()));
        }

        if (endTime != null) {
            Date _endTime = DateUtil.addSeconds(endTime, 1);
            _query = _query.where(i -> _endTime.after(i.getCreateTime()));
        }

        if (CollectionUtils.isEmpty(sorters)) {
            _query = _query.sortedDescendingBy(AbstractEntity::getCreateTime);
        } else {
            _query = sort(_query, sorters);
        }

        PaginatedRecords<SentSms> _page = new PaginatedRecords<>(count, index);
        _page.total = _query.count();

        if (count >= 0 && index >= 0) {
            _query = _query.skip((long) count * index).limit(count);
        }
        _page.records = _query.toList();

        for (SentSms i : _page.records) {
            if (StringUtils.hasLength(i.getMpKeys())) {
                i.setMps(ConverterUtil.json2Object(i.getMpKeys(), new TypeToken<List<String>>() {
                }.getType()));
            }
        }

        return _page;
    }

    public Result<Void> cancel(String id, String operatorId) {
        Result<Void> _success = new Result<>();

        SentSms _record = stream(SentSms.class).where(i -> id.equals(i.getId()))
                .findFirst().orElse(null);
        if (_record == null) {
            _success.setCode(Result.DATABASE_RECORD_NOT_FOUND, new Object[]{SentSms.class.getSimpleName()});
            return _success;
        }

        if (SentSms.ENUM_STATUS.TODO != _record.getStatus()) {
            _success.setCode(Result.DATA_ACCESS_DENY);
            return _success;
        }

        _record.setStatus(SentSms.ENUM_STATUS.CANCELED);
        return update(_record, operatorId);
    }

    public Pair<Integer, Integer> sendSms() {
        int _success = 0, _fail = 0;

        SentSms.ENUM_STATUS _todo = SentSms.ENUM_STATUS.TODO;
        List<SentSms> _records = stream(SentSms.class)
                .where(i -> _todo == i.getStatus())
                .toList();

        if (masFacade == null) {
            for (SentSms i : _records) {
                i.setStatus(SentSms.ENUM_STATUS.CANCELED);

                update(i, null);
            }
        } else {
            for (SentSms i : _records) {
                List<String> _mps = ConverterUtil.json2Object(i.getMpKeys(), new TypeToken<List<String>>() {
                }.getType());

                Result<String> _sn;
                // 发送模板短信
                if (StringUtils.hasLength(i.getTemplateId())) {
                    List<String> _params = ConverterUtil.json2Object(i.getParams(), new TypeToken<List<String>>() {
                    }.getType());
                    _sn = masFacade.sendSms(_mps, i.getTemplateId(), _params, i.getSign());
                }
                // 发送普通短信
                else {
                    _sn = masFacade.sendSms(_mps, i.getParams(), i.getSign());
                }

                if (_sn.isOK()) {
                    i.setSn(_sn.data);
                    i.setStatus(SentSms.ENUM_STATUS.DONE);

                    _success++;
                } else {
                    i.setResponseCode(_sn.getCode());
                    i.setStatus(SentSms.ENUM_STATUS.FAILED);

                    _fail++;
                }

                update(i, null);
            }
        }

        return Pair.of(_success, _fail);
    }

}