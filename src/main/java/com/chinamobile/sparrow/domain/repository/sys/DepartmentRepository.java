package com.chinamobile.sparrow.domain.repository.sys;

import com.chinamobile.sparrow.domain.infra.code.ErrorCode;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.lang.PaginatedRecords;
import com.chinamobile.sparrow.domain.lang.Sorter;
import com.chinamobile.sparrow.domain.model.AbstractEntity;
import com.chinamobile.sparrow.domain.model.sys.Department;
import com.chinamobile.sparrow.domain.model.sys.User;
import com.chinamobile.sparrow.domain.repository.AbstractEntityRepository;
import org.jinq.jpa.JinqJPAStreamProvider;
import org.jinq.orm.stream.JinqStream;
import org.jinq.tuples.Pair;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.persistence.EntityManagerFactory;
import java.util.*;
import java.util.function.BiFunction;
import java.util.stream.Collectors;

@ErrorCode(module = "003")
public class DepartmentRepository<T extends Department> extends AbstractEntityRepository<T> {

    protected final UserRepository userRepository;

    public DepartmentRepository(EntityManagerFactory entityManagerFactory, JinqJPAStreamProvider jinqJPAStreamProvider, Class<T> tClass, UserRepository userRepository) {
        super(entityManagerFactory, jinqJPAStreamProvider, tClass);
        this.userRepository = userRepository;
    }

    @Override
    protected <V extends Comparable<V>> JinqStream<T> sort(JinqStream<T> query, BiFunction<JinqStream<T>, JinqStream.CollectComparable<T, V>, JinqStream<T>> compare, String field) {
        switch (field) {
            case "createTime":
                return compare.apply(query, i -> (V) i.getCreateTime());
            case "fullName":
                return compare.apply(query, i -> (V) i.getFullName());
            case "name":
                return compare.apply(query, i -> (V) i.getName());
            case "seq":
                return compare.apply(query, i -> (V) i.getSeq());
            default:
                return query;
        }
    }

    @Transactional(readOnly = true)
    public Result<T> get(String id, Boolean isEnabled) {
        Result<T> _record = new Result<>();

        if (!StringUtils.hasLength(id)) {
            _record.setCode(Result.DATABASE_RECORD_NOT_FOUND, new Object[]{Department.class.getSimpleName()});
        }

        _record.data = stream(tClass)
                .where(i -> i.getId().equals(id))
                .where(i -> isEnabled == null || isEnabled.equals(i.getIsEnabled()))
                .findFirst().orElse(null);
        if (_record.data == null) {
            _record.setCode(Result.DATABASE_RECORD_NOT_FOUND, new Object[]{Department.class.getSimpleName()});
        }

        return _record;
    }

    @Transactional(readOnly = true)
    public List<T> find(List<String> ids, List<String> names, Integer level, Boolean isEnabled, List<Sorter> sorters) {
        return find(-1, -1, sorters, ids, names, level, isEnabled).records;
    }

    @Transactional(readOnly = true)
    public PaginatedRecords<T> find(int count, int index, List<Sorter> sorters, List<String> ids, List<String> names, Integer level, Boolean isEnabled) {
        JinqStream<T> _query = stream(tClass);

        if (ids != null) {
            _query = _query.where(i -> ids.contains(i.getId()));
        }

        if (names != null) {
            _query = _query.where(i -> names.contains(i.getName()));
        }

        if (level != null) {
            _query = _query.where(i -> level == i.getLevel());
        }

        if (isEnabled != null) {
            _query = _query.where(i -> isEnabled == i.getIsEnabled());
        }

        if (CollectionUtils.isEmpty(sorters)) {
            _query = _query.sortedBy(AbstractEntity::getCreateTime);
        } else {
            _query = sort(_query, sorters);
        }

        PaginatedRecords<T> _page = new PaginatedRecords<>(count, index);
        _page.total = _query.count();

        if (count >= 0 && index >= 0) {
            _query = _query.skip((long) count * index).limit(count);
        }
        _page.records = _query.toList();

        return _page;
    }

    public Result<String> save(T record, String operatorId) throws InstantiationException, IllegalAccessException {
        Result<String> _id = new Result<>();

        boolean _alreadyExisted = true;

        Result<T> _record = ((DepartmentRepository<T>) proxy).get(record.getId(), true);
        if (!_record.isOK()) {
            _record.data = tClass.newInstance();

            // 无上级部门
            if (!StringUtils.hasLength(record.getSuperiorId())) {
                _record.data.setCode(_record.data.getId());
                _record.data.setFullName(record.getName());
                _record.data.setLevel(1);
            }
            // 设置上级部门
            else {
                Result<T> _superior = ((DepartmentRepository<T>) proxy).get(record.getSuperiorId(), true);
                if (_superior.isOK()) {
                    _record.data.setSuperiorId(_superior.data.getId());
                    _record.data.setCode(_superior.data.getCode() + Department.CODE_SEPARATOR + _record.data.getId());
                    _record.data.setFullName(_superior.data.getFullName() + Department.NAME_SEPARATOR + record.getName());
                    _record.data.setLevel(_superior.data.getLevel() + 1);
                }
            }

            _alreadyExisted = false;
        }

        String _identifier = _record.data.getId();
        JinqStream<T> _query = stream(tClass).where(i -> _identifier == null || !_identifier.equals(i.getId()));

        String _name = record.getName();
        String _superiorId = record.getSuperiorId();
        if (_query.where(i -> _name == null || _name.equals(i.getName()))
                .where(i -> _superiorId == null || _superiorId.equals(i.getSuperiorId()))
                .where(Department::getIsEnabled)
                .findFirst().isPresent()) {
            _id.setCode(Result.DATABASE_RECORD_ALREADY_EXIST);
            return _id;
        }

        // 修改名称，需级联修改下级单位的部门全称
        if (_alreadyExisted && !Objects.equals(_record.data.getName(), record.getName())) {
            int _index = _record.data.getLevel() - 1;
            for (T i : ((DepartmentRepository<T>) proxy).subordinates(_record.data.getId(), null, null)) {
                // 跳过本身
                if (Objects.equals(i.getId(), _record.data.getId())) {
                    continue;
                }

                List<String> _names = Arrays.asList(i.getFullName().split(Department.NAME_SEPARATOR));
                if (_names.size() > _index) {
                    _names.set(_index, record.getName());
                }

                i.setFullName(String.join(Department.NAME_SEPARATOR, _names));
                update(i, operatorId);
            }
        }

        _record.data.setName(record.getName());
        _record.data.setSeq(record.getSeq() == null ? Integer.MAX_VALUE : record.getSeq());

        Result<Void> _success = _alreadyExisted ? update(_record.data, operatorId) : add(_record.data, operatorId);
        if (!_success.isOK()) {
            return _id.pack(_success);
        }

        _id.data = _record.data.getId();
        return _id;
    }

    public Result<Void> move(String id, String superiorId, String operatorId) {
        Result<Void> _success = new Result<>();

        if (Objects.equals(id, superiorId)) {
            _success.setCode(Result.ENUM_ERROR.P, 1);
            return _success;
        }

        Result<T> _record = ((DepartmentRepository<T>) proxy).get(id, true);
        if (!_record.isOK()) {
            return _success.pack(_record);
        }

        // 已存在同名部门
        String _identifier = _record.data.getId();
        JinqStream<T> _query = stream(tClass).where(i -> _identifier == null || _identifier.equals(i.getId()));

        String _name = _record.data.getName();
        if (_query.where(i -> _name == null || _name.equals(i.getName()))
                .where(i -> (superiorId == null && i.getSuperiorId() == null) || (superiorId != null && superiorId.equals(i.getSuperiorId())))
                .where(Department::getIsEnabled)
                .findFirst().isPresent()) {
            _success.setCode(Result.ENUM_ERROR.P, 2);
            return _success;
        }

        int _oldLevel = _record.data.getLevel();

        // 移动到根节点下
        if (superiorId == null) {
            _record.data.setSuperiorId(null);
            _record.data.setCode(_record.data.getId());
            _record.data.setFullName(_record.data.getName());
            _record.data.setLevel(1);
        }
        // 其它
        else {
            Result<T> _superior = ((DepartmentRepository<T>) proxy).get(superiorId, true);
            if (!_superior.isOK()) {
                _success.setCode(Result.ENUM_ERROR.P, 3);
                return _success;
            }

            _record.data.setSuperiorId(superiorId);
            _record.data.setCode(_superior.data.getCode() + Department.CODE_SEPARATOR + _record.data.getId());
            _record.data.setFullName(_superior.data.getFullName() + Department.NAME_SEPARATOR + _record.data.getName());
            _record.data.setLevel(_superior.data.getLevel() + 1);
        }

        // 级联修改下级单位的代码、部门全称和级别
        for (T i : ((DepartmentRepository<T>) proxy).subordinates(_record.data.getId(), null, null)) {
            // 跳过本身
            if (Objects.equals(i.getId(), _record.data.getId())) {
                continue;
            }

            StringBuilder _newCode = new StringBuilder(_record.data.getCode());
            StringBuilder _newFullName = new StringBuilder(_record.data.getFullName());

            List<String> _codes = Arrays.asList(i.getCode().split("\\" + Department.CODE_SEPARATOR));
            List<String> _names = Arrays.asList(i.getFullName().split(Department.NAME_SEPARATOR));

            for (int j = _oldLevel; j < _codes.size(); j++) {
                _newCode.append(Department.CODE_SEPARATOR).append(_codes.get(j));
                _newFullName.append(Department.NAME_SEPARATOR).append(_names.get(j));
            }

            i.setCode(_newCode.toString());
            i.setFullName(_newFullName.toString());
            i.setLevel(i.getLevel() - _oldLevel + _record.data.getLevel());
            update(i, operatorId);
        }

        return update(_record.data, operatorId);
    }

    public Result<Void> disable(String id, String operatorId) throws InstantiationException, IllegalAccessException {
        Result<Void> _success = new Result<>();

        Result<T> _record = ((DepartmentRepository<T>) proxy).get(id, true);
        if (!_record.isOK()) {
            return _success.pack(_record);
        }

        Result<Organization<T>> _org = ((DepartmentRepository<T>) proxy).getAsOrganization(id);
        if (!_org.isOK()) {
            return _success.pack(_org);
        }

        if (!CollectionUtils.isEmpty(_org.data.subordinates)) {
            _success.setCode(Result.ENUM_ERROR.B, 1);
            return _success;
        }

        if (!CollectionUtils.isEmpty(_org.data.members)) {
            _success.setCode(Result.ENUM_ERROR.B, 2);
            return _success;
        }

        _record.data.setIsEnabled(false);
        return update(_record.data, operatorId);
    }

    /**
     * 获取下级单位
     *
     * @param id
     * @param peerLv   匹配某一级别的下级单位
     * @param lowestLv 满足最低级别的下级单位
     * @return
     */
    @Transactional(readOnly = true)
    public List<T> subordinates(String id, Integer peerLv, Integer lowestLv) {
        JinqStream<T> _query;

        List<T> _records = new ArrayList<>();
        if (!StringUtils.hasLength(id)) {
            _query = stream(tClass);
        } else {
            Result<T> _record = get(id, null);
            if (!_record.isOK()) {
                return new ArrayList<>();
            }
            _records.add(_record.data);

            String _prefix = _record.data.getCode() + Department.CODE_SEPARATOR;
            _query = stream(tClass)
                    .where(i -> i.getCode().startsWith(_prefix) && i.getIsEnabled());
        }

        if (peerLv != null) {
            if (!_records.isEmpty() && _records.get(0).getLevel() != peerLv) {
                _records.remove(0);
            }

            _query = _query.where(i -> peerLv == i.getLevel());
        }

        if (lowestLv != null) {
            if (!_records.isEmpty() && _records.get(0).getLevel() > lowestLv) {
                _records.remove(0);
            }

            _query = _query.where(i -> lowestLv >= i.getLevel());
        }

        _query = _query.sortedBy(Department::getSeq)
                .sortedBy(Department::getLevel);

        if (_query.count() > 0) {
            _records.addAll(_query.toList());
        }

        return _records;
    }

    /**
     * 搜索成员
     *
     * @param id
     * @param account
     * @param username
     * @param mp
     * @param sorters
     * @return
     */
    @Transactional(readOnly = true)
    public List<User> members(String id, String account, String username, String mp, Boolean isEnabled, List<Sorter> sorters) {
        return members(-1, -1, sorters, id, account, username, mp, isEnabled).records;
    }

    /**
     * 搜索成员
     *
     * @param count
     * @param index
     * @param sorters
     * @param id
     * @param account
     * @param username
     * @param mp
     * @return
     */
    @Transactional(readOnly = true)
    public PaginatedRecords<User> members(int count, int index, List<Sorter> sorters, String id, String account, String username, String mp, Boolean isEnabled) {
        JinqStream<Pair<T, User>> _query = stream(tClass)
                .leftOuterJoin((i, session) -> session.stream(User.class), (i, user) -> i.getId().equals(user.getDeptId()))
                .where(i -> i.getTwo() != null);

        if (StringUtils.hasLength(id)) {
            String _patten1 = id + Department.CODE_SEPARATOR, _patten2 = Department.CODE_SEPARATOR + _patten1;
            _query = _query.where(i -> i.getOne().getId().equals(id) || i.getOne().getCode().startsWith(_patten1) || i.getOne().getCode().contains(_patten2));
        }

        if (StringUtils.hasLength(account)) {
            _query = _query.where(i -> i.getTwo().getAccount().contains(account));
        }

        if (StringUtils.hasLength(username)) {
            _query = _query.where(i -> i.getTwo().getName().contains(username));
        }

        if (StringUtils.hasLength(mp)) {
            _query = _query.where(i -> i.getTwo().getMp() != null && i.getTwo().getMp().contains(mp));
        }

        if (isEnabled != null) {
            _query = _query.where(i -> isEnabled == i.getTwo().getIsEnabled());
        }

        if (CollectionUtils.isEmpty(sorters)) {
            _query = _query.sortedBy(i -> i.getTwo().getName())
                    .sortedBy(i -> i.getTwo().getSeq());
        } else {
            Collections.reverse(sorters);
            for (Sorter i : sorters) {
                switch (i.getField()) {
                    case "account":
                        _query = i.getAsc() ? _query.sortedBy(j -> j.getTwo().getAccount()) : _query.sortedDescendingBy(j -> j.getTwo().getAccount());
                        break;
                    case "alphabet":
                        _query = i.getAsc() ? _query.sortedBy(j -> j.getTwo().getAlphabet()) : _query.sortedDescendingBy(j -> j.getTwo().getAlphabet());
                        break;
                    case "birthday":
                        _query = i.getAsc() ? _query.sortedBy(j -> j.getTwo().getBirthday()) : _query.sortedDescendingBy(j -> j.getTwo().getBirthday());
                        break;
                    case "createTime":
                        _query = i.getAsc() ? _query.sortedBy(j -> j.getTwo().getCreateTime()) : _query.sortedDescendingBy(j -> j.getTwo().getCreateTime());
                        break;
                    case "mp":
                        _query = i.getAsc() ? _query.sortedBy(j -> j.getTwo().getMp()) : _query.sortedDescendingBy(j -> j.getTwo().getMp());
                        break;
                    case "name":
                        _query = i.getAsc() ? _query.sortedBy(j -> j.getTwo().getName()) : _query.sortedDescendingBy(j -> j.getTwo().getName());
                        break;
                    case "seq":
                        _query = i.getAsc() ? _query.sortedBy(j -> j.getTwo().getSeq()) : _query.sortedDescendingBy(j -> j.getTwo().getSeq());
                        break;
                }
            }
        }

        PaginatedRecords<User> _page = new PaginatedRecords<>(count, index);
        _page.total = _query.count();
        _page.records = new ArrayList<>();

        if (count >= 0 && index >= 0) {
            _query = _query.skip((long) count * index).limit(count);
        }

        for (Pair<T, User> i : _query.toList()) {
            User _user = i.getTwo();
            _user.setDeptName(i.getOne().getName());
            _user.setDeptCode(i.getOne().getCode());
            _user.setDeptFullName(i.getOne().getFullName());

            _page.records.add(_user);
        }

        return _page;
    }

    /**
     * 获取部门、同级成员、下级部门
     *
     * @param id
     * @return
     */
    @Transactional(readOnly = true)
    public Result<Organization<T>> getAsOrganization(String id) throws InstantiationException, IllegalAccessException {
        Result<Organization<T>> _org = new Result<>();
        _org.data = new Organization<>();

        List<T> _departments = stream(tClass).toList();

        // 读取部门
        if (id != null) {
            _org.data.department = _departments.stream()
                    .filter(i -> Objects.equals(id, i.getId()) && i.getIsEnabled())
                    .findFirst().orElse(null);
        } else {
            _org.data.department = tClass.newInstance();
            _org.data.department.setId(null);
            _org.data.department.setCode("");
            _org.data.department.setFullName("");
        }

        if (_org.data.department == null) {
            _org.setCode(Result.DATABASE_RECORD_NOT_FOUND, new Object[]{Department.class.getSimpleName()});
            return _org;
        }

        // 读取下级单位
        _org.data.subordinates = _departments.stream()
                .filter(i -> Objects.equals(id, i.getSuperiorId()) && i.getIsEnabled())
                .sorted(Comparator.comparing(Department::getLevel, Comparator.nullsFirst(Comparator.naturalOrder()))
                        .thenComparing(Department::getSeq, Comparator.nullsFirst(Comparator.naturalOrder())))
                .collect(Collectors.toList());
        for (T i : _org.data.subordinates) {
            i.setHasSubordinates(_departments.stream()
                    .anyMatch(j -> Objects.equals(i.getId(), j.getSuperiorId())));
        }

        _org.data.department.setHasSubordinates(!_org.data.subordinates.isEmpty());

        // 读取人员
        _org.data.members = new ArrayList<>();
        if (id != null) {
            _org.data.members = (List<User>) userRepository.find(null, Collections.singletonList(id), null, null, null, true, null).stream()
                    .map(i -> userRepository.mask((User) i))
                    .collect(Collectors.toList());

            _org.data.members = _org.data.members.stream()
                    .sorted(Comparator.comparing(User::getSeq, Comparator.nullsFirst(Comparator.naturalOrder()))
                            .thenComparing(User::getName, Comparator.nullsFirst(Comparator.naturalOrder())))
                    .collect(Collectors.toList());
        }

        return _org;
    }

    public static class Organization<T extends Department> {

        public T department;
        public List<User> members;
        public List<T> subordinates;

    }

}