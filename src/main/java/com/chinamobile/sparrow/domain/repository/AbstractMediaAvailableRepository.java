package com.chinamobile.sparrow.domain.repository;

import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.model.AbstractEntity;
import com.chinamobile.sparrow.domain.repository.media.AbstractMediaRepository;
import org.jinq.jpa.JinqJPAStreamProvider;

import javax.persistence.EntityManagerFactory;
import java.util.Collections;
import java.util.List;

public abstract class AbstractMediaAvailableRepository<T extends AbstractEntity> extends AbstractEntityRepository<T> {

    protected final AbstractMediaRepository mediaRepository;

    public AbstractMediaAvailableRepository(EntityManagerFactory entityManagerFactory, JinqJPAStreamProvider jinqJPAStreamProvider, AbstractMediaRepository mediaRepository, Class<T> tClass) {
        super(entityManagerFactory, jinqJPAStreamProvider, tClass);
        this.mediaRepository = mediaRepository;
    }

    @Override
    public Result<Void> add(T record, String operatorId) {
        // 将临时文件转为正式文件
        List<String> _mediaIds = getMediaIds(Collections.singletonList(record));
        mediaRepository.formalize(_mediaIds, operatorId);

        return super.add(record, operatorId);
    }

    @Override
    public Result<Void> update(T record, String operatorId) {
        // 将临时文件转为正式文件
        List<String> _mediaIds = getMediaIds(Collections.singletonList(record));
        mediaRepository.formalize(_mediaIds, operatorId);

        return super.update(record, operatorId);
    }

    public void remove(T record, String operatorId) {
        // 删除文件
        List<String> _mediaIds = getMediaIds(Collections.singletonList(record));
        for (String i : _mediaIds) {
            mediaRepository.remove(i, true, operatorId);
        }

        getCurrentSession().remove(record);
    }

    /**
     * 读取关联文件
     *
     * @param records
     * @return
     */
    public abstract List<String> getMediaIds(List<T> records);

}