package com.chinamobile.sparrow.domain.repository.media;

import com.chinamobile.sparrow.domain.infra.code.ErrorCode;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.lang.PaginatedRecords;
import com.chinamobile.sparrow.domain.lang.ResultWarpperRuntimeException;
import com.chinamobile.sparrow.domain.lang.Sorter;
import com.chinamobile.sparrow.domain.model.AbstractEntity;
import com.chinamobile.sparrow.domain.model.media.Media;
import com.chinamobile.sparrow.domain.repository.AbstractEntityRepository;
import com.chinamobile.sparrow.domain.util.FileUtil;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.pdfbox.cos.COSName;
import org.apache.pdfbox.io.RandomAccessBuffer;
import org.apache.pdfbox.io.RandomAccessRead;
import org.apache.pdfbox.pdfparser.PDFParser;
import org.jinq.jpa.JinqJPAStreamProvider;
import org.jinq.orm.stream.JinqStream;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.persistence.EntityManagerFactory;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.function.BiFunction;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Stream;

@ErrorCode(module = "005")
public abstract class AbstractMediaRepository extends AbstractEntityRepository<Media> {

    protected final String[] extensionsAllow;
    protected final String[] extensionsForbid;
    protected final Logger logger;

    public AbstractMediaRepository(EntityManagerFactory entityManagerFactory, JinqJPAStreamProvider jinqJPAStreamProvider, String extensionsAllow, String extensionsForbid) {
        super(entityManagerFactory, jinqJPAStreamProvider, Media.class);
        this.extensionsAllow = StringUtils.hasLength(extensionsAllow) ? extensionsAllow.split(",") : new String[0];
        this.extensionsForbid = StringUtils.hasLength(extensionsForbid) ? extensionsForbid.split(",") : new String[0];
        this.logger = LoggerFactory.getLogger(this.getClass());
    }

    @Override
    protected <V extends Comparable<V>> JinqStream<Media> sort(JinqStream<Media> query, BiFunction<JinqStream<Media>, JinqStream.CollectComparable<Media, V>, JinqStream<Media>> compare, String field) {
        switch (field) {
            case "createTime":
                return compare.apply(query, i -> (V) i.getCreateTime());
            case "isTemporary":
                return compare.apply(query, i -> (V) ((Object) i.getIsTemporary()));
            case "name":
                return compare.apply(query, i -> (V) i.getName());
            case "path":
                return compare.apply(query, i -> (V) i.getPath());
            default:
                return query;
        }
    }

    @Transactional(readOnly = true)
    public Result<Media> get(String id, String userId) {
        Result<Media> _record = new Result<>();

        if (!StringUtils.hasLength(id)) {
            _record.setCode(Result.DATABASE_RECORD_NOT_FOUND, new Object[]{Media.class.getSimpleName()});
            return _record;
        }

        _record.data = getCurrentSession().get(Media.class, id);
        if (_record.data == null) {
            _record.setCode(Result.DATABASE_RECORD_NOT_FOUND, new Object[]{Media.class.getSimpleName()});
            return _record;
        }

        _record.data.setEnableEdit(Objects.equals(userId, _record.data.getCreatorId()));

        return _record;
    }

    @Transactional(readOnly = true)
    public Result<Media> permit(String id, String userId) {
        Result<Media> _record = get(id, userId);
        if (!_record.isOK()) {
            return _record;
        }

        if (!_record.data.getEnableEdit()) {
            _record.setCode(Result.DATA_ACCESS_DENY);
        }

        return _record;
    }

    @Transactional(readOnly = true)
    public List<Media> search(String path, String name, Boolean temporary, List<Sorter> sorters) {
        return search(-1, -1, sorters, path, name, temporary).records;
    }

    @Transactional(readOnly = true)
    public PaginatedRecords<Media> search(int count, int index, List<Sorter> sorters, String path, String name, Boolean temporary) {
        JinqStream<Media> _query = stream(Media.class);

        if (StringUtils.hasLength(path)) {
            _query = _query.where(i -> i.getPath() != null && i.getPath().startsWith(path));
        }

        if (StringUtils.hasLength(name)) {
            _query = _query.where(i -> i.getName() != null && i.getName().contains(name));
        }

        if (temporary != null) {
            _query = _query.where(i -> temporary == i.getIsTemporary());
        }

        if (CollectionUtils.isEmpty(sorters)) {
            _query = _query.sortedBy(Media::getName)
                    .sortedDescendingBy(AbstractEntity::getCreateTime);
        } else {
            _query = sort(_query, sorters);
        }

        PaginatedRecords<Media> _page = new PaginatedRecords<>(count, index);
        _page.total = _query.count();

        if (count >= 0 && index >= 0) {
            _query = _query.skip((long) count * index).limit(count);
        }
        _page.records = _query.toList();

        return _page;
    }

    @Transactional(readOnly = true)
    public List<Media> find(List<String> ids, List<Sorter> sorters) {
        return find(-1, -1, sorters, ids).records;
    }

    @Transactional(readOnly = true)
    public PaginatedRecords<Media> find(int count, int index, List<Sorter> sorters, List<String> ids) {
        JinqStream<Media> _query = stream(Media.class);

        if (ids != null) {
            _query = _query.where(i -> ids.contains(i.getId()));
        }

        if (CollectionUtils.isEmpty(sorters)) {
            _query = _query.sortedBy(Media::getName)
                    .sortedDescendingBy(AbstractEntity::getCreateTime);
        } else {
            _query = sort(_query, sorters);
        }

        PaginatedRecords<Media> _page = new PaginatedRecords<>(count, index);
        _page.total = _query.count();

        if (count >= 0 && index >= 0) {
            _query = _query.skip((long) count * index).limit(count);
        }
        _page.records = _query.toList();

        return _page;
    }

    public abstract List<ImmutablePair<String, String>> getSubPaths(String path);

    /**
     * 暂存
     *
     * @param bucket
     * @param fileName
     * @param base64
     * @param operatorId
     * @return
     */
    public abstract Result<Media> stage(String bucket, String fileName, String base64, String operatorId);

    /**
     * 暂存
     *
     * @param bucket
     * @param file
     * @param operatorId
     * @return
     */
    public abstract Result<Media> stage(String bucket, File file, String operatorId);

    protected Result<Media> stage(Function<String, Result<String>> func, String path, String fileName, String operatorId) {
        return add(func, path, fileName, true, true, operatorId);
    }

    public abstract Result<Media> add(String bucket, String fileName, String base64, boolean saveFile, String operatorId);

    public abstract Result<Media> add(String bucket, File file, boolean saveFile, String operatorId);

    protected Result<Media> add(Function<String, Result<String>> func, String path, String fileName, boolean saveFile, boolean temporary, String operatorId) {
        Result<Media> _record = new Result<>();
        _record.data = new Media();
        _record.data.setName(fileName);
        _record.data.setExtension(FilenameUtils.getExtension(fileName));

        if (saveFile) {
            if (!allow(fileName)) {
                _record.setCode(Result.ENUM_ERROR.P, 2, new Object[]{FilenameUtils.getExtension(fileName)});

                return _record;
            }

            Result<String> _url = func.apply(_record.data.getId());
            if (!_url.isOK()) {
                return _record.pack(_url);
            }

            if (!StringUtils.hasLength(_url.data)) {
                _record.setCode(Result.ENUM_ERROR.B, 1);
                return _record;
            }

            _record.data.setPath(path);
            _record.data.setContent(_url.data);
            _record.data.setIsTemporary(temporary);
        }

        // 保存到数据库
        Result<Void> _success = add(_record.data, operatorId);
        if (!_success.isOK()) {
            return _record.pack(_success);
        }

        return _record;
    }

    /**
     * 临时文件转为正式文件
     *
     * @param ids
     */
    public int formalize(List<String> ids, String operatorId) {
        return CollectionUtils.isEmpty(ids) ? 0 : getCurrentSession().createQuery("update Media set isTemporary = false, maintainerId = ?1, maintainTime = ?2 where id in ?3 and isTemporary = true")
                .setParameter(1, operatorId)
                .setParameter(2, new Date())
                .setParameter(3, ids)
                .executeUpdate();
    }

    public abstract Result<Void> remove(String id, boolean force, String operatorId);

    public abstract Result<Void> remove(Media record, boolean force, String operatorId);

    protected Result<Void> remove(Function<Media, Result<Void>> func, String id, boolean force, String operatorId) {
        Result<Void> _success = new Result<>();

        Result<Media> _record = force ? ((AbstractMediaRepository) proxy).get(id, operatorId) : ((AbstractMediaRepository) proxy).permit(id, operatorId);
        if (!_record.isOK()) {
            return _success.pack(_record);
        }

        getCurrentSession().remove(_record.data);

        // 删除文件
        if (StringUtils.hasLength(_record.data.getExtension())) {
            Result<Void> _temp = func.apply(_record.data);
            if (!_temp.isOK()) {
                // 回滚事务
                throw new ResultWarpperRuntimeException(_temp);
            }
        }

        return _success;
    }

    /**
     * 清理临时文件
     *
     * @param operatorId
     * @return
     */
    public int clearTemporary(String operatorId) {
        List<Media> _records = stream(Media.class).where(Media::getIsTemporary)
                .toList();
        for (Media i : _records) {
            try {
                remove(i, true, operatorId);
            } catch (Throwable e) {
                logger.error(String.format("清理临时文件[%s]失败", i.getId()), e);
            }
        }

        return _records.size();
    }

    /**
     * 读取为文件流
     *
     * @param id
     * @param userId
     * @return
     */
    @Transactional(readOnly = true)
    public abstract Result<InputStream> getInputStream(String id, String userId);

    @Transactional(readOnly = true)
    public abstract Result<InputStream> getInputStream(Media record);

    protected Result<InputStream> getInputStream(Function<Media, Result<InputStream>> func, String id, String userId) {
        Result<Media> _record = ((AbstractMediaRepository) proxy).get(id, userId);
        return _record.isOK() ? func.apply(_record.data) : new Result<InputStream>().pack(_record);
    }

    /**
     * 读取为字节数组
     *
     * @param id
     * @param userId
     * @return
     * @throws IOException
     */
    @Transactional(readOnly = true)
    public abstract Result<byte[]> getBytes(String id, String userId) throws IOException;

    @Transactional(readOnly = true)
    public abstract Result<byte[]> getBytes(Media record) throws IOException;

    protected Result<byte[]> getBytes(Supplier<Result<InputStream>> supplier) throws IOException {
        Result<byte[]> _bytes = new Result<>();

        Result<InputStream> _stream = supplier.get();
        if (!_stream.isOK()) {
            return _bytes.pack(_stream);
        }

        _bytes.data = FileUtil.readInputStreamToByteArray(_stream.data);
        return _bytes;
    }

    /**
     * 编码为Base64
     *
     * @param id
     * @param userId
     * @return
     * @throws Exception
     */
    @Transactional(readOnly = true)
    public abstract Result<String> getBase64String(String id, String userId) throws Exception;

    @Transactional(readOnly = true)
    public abstract Result<String> getBase64String(Media record) throws Exception;

    protected Result<String> getBase64String(Supplier<Result<byte[]>> supplier) {
        Result<String> _str = new Result<>();

        Result<byte[]> _bytes = supplier.get();
        if (!_bytes.isOK()) {
            return _str.pack(_bytes);
        }

        _str.data = FileUtil.readByteArrayToBase64String(_bytes.data);
        return _str;
    }

    /**
     * 读取分块
     *
     * @param id
     * @param start
     * @param end
     * @param userId
     * @return
     */
    @Transactional(readOnly = true)
    public abstract Result<byte[]> getPart(String id, Long start, Long end, String userId);

    @Transactional(readOnly = true)
    public abstract Result<byte[]> getPart(Media record, Long start, Long end);

    protected Result<byte[]> getPart(Function<Media, Result<byte[]>> func, String id, String userId) {
        Result<Media> _record = ((AbstractMediaRepository) proxy).get(id, userId);
        return _record.isOK() ? func.apply(_record.data) : new Result<byte[]>().pack(_record);
    }

    /**
     * 获取文件长度（字节）
     *
     * @param id
     * @param userId
     * @return
     */
    @Transactional(readOnly = true)
    public abstract Result<Long> getContentLength(String id, String userId);

    @Transactional(readOnly = true)
    public abstract Result<Long> getContentLength(Media record);

    protected Result<Long> getContentLength(Function<Media, Result<Long>> func, String id, String userId) {
        Result<Media> _record = ((AbstractMediaRepository) proxy).get(id, userId);
        return _record.isOK() ? func.apply(_record.data) : new Result<Long>().pack(_record);
    }

    public abstract String path(String path);

    protected boolean allow(String fileName) {
        String _extension = FilenameUtils.getExtension(fileName).toLowerCase();
        return Arrays.asList(extensionsAllow).contains(_extension) && Stream.of(extensionsForbid).noneMatch(i -> Objects.equals(i, _extension));
    }

    protected Result<Void> xss(String fileName, byte[] bytes) {
        Result<Void> _success = new Result<>();

        if ("pdf".equals(FilenameUtils.getExtension(fileName))) {
            try (RandomAccessRead file = new RandomAccessBuffer(bytes)) {
                String _javascript = COSName.JAVA_SCRIPT.getCOSObject().toString();
                String _js = COSName.JS.getCOSObject().toString();

                PDFParser _parser = new PDFParser(file);
                _parser.parse();
                String _trailer = _parser.getDocument().getTrailer().toString();
                if (_trailer.contains(_javascript) || _trailer.contains(_js)) {
                    _success.setCode(Result.ENUM_ERROR.P, 3);
                    return _success;
                }
            } catch (Throwable e) {
                _success.setCode(Result.ENUM_ERROR.P, 4, new Object[]{e.getMessage()});
                return _success;
            }
        }

        return _success;
    }

}