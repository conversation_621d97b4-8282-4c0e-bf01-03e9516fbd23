package com.chinamobile.sparrow.domain.repository.sys;

import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.lang.PaginatedRecords;
import com.chinamobile.sparrow.domain.lang.Sorter;
import com.chinamobile.sparrow.domain.model.sys.Dictionary;
import com.chinamobile.sparrow.domain.repository.AbstractEntityRepository;
import org.jinq.jpa.JinqJPAStreamProvider;
import org.jinq.orm.stream.JinqStream;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.persistence.EntityManagerFactory;
import java.util.List;
import java.util.function.BiFunction;

public class DictionaryRepository extends AbstractEntityRepository<Dictionary> {

    public DictionaryRepository(EntityManagerFactory entityManagerFactory, JinqJPAStreamProvider jinqJPAStreamProvider) {
        super(entityManagerFactory, jinqJPAStreamProvider, Dictionary.class);
    }

    @Override
    protected <V extends Comparable<V>> JinqStream<Dictionary> sort(JinqStream<Dictionary> query, BiFunction<JinqStream<Dictionary>, JinqStream.CollectComparable<Dictionary, V>, JinqStream<Dictionary>> compare, String field) {
        switch (field) {
            case "createTime":
                return compare.apply(query, i -> (V) i.getCreateTime());
            case "groupId":
                return compare.apply(query, i -> (V) i.getGroupId());
            case "name":
                return compare.apply(query, i -> (V) i.getName());
            case "seq":
                return compare.apply(query, i -> (V) i.getSeq());
            case "val":
                return compare.apply(query, i -> (V) i.getVal());
            default:
                return query;
        }
    }

    @Transactional(readOnly = true)
    public Result<Dictionary> get(String id, Boolean isEnabled) {
        Result<Dictionary> _record = new Result<>();

        if (!StringUtils.hasLength(id)) {
            _record.setCode(Result.DATABASE_RECORD_NOT_FOUND, new Object[]{Dictionary.class.getSimpleName()});
            return _record;
        }

        _record.data = stream(Dictionary.class)
                .where(i -> id.equals(i.getId()))
                .where(i -> isEnabled == null || isEnabled == i.getIsEnabled())
                .findFirst().orElse(null);
        if (_record.data == null) {
            _record.setCode(Result.DATABASE_RECORD_NOT_FOUND, new Object[]{Dictionary.class.getSimpleName()});
        }

        return _record;
    }

    @Transactional(readOnly = true)
    public Result<Dictionary> get(String groupId, String name, Boolean isEnabled) {
        Result<Dictionary> _record = new Result<>();

        _record.data = stream(Dictionary.class)
                .where(i -> groupId == null || groupId.equals(i.getGroupId()))
                .where(i -> name == null || name.equals(i.getName()))
                .where(i -> isEnabled == null || isEnabled == i.getIsEnabled())
                .findFirst().orElse(null);
        if (_record.data == null) {
            _record.setCode(Result.DATABASE_RECORD_NOT_FOUND, new Object[]{Dictionary.class.getSimpleName()});
        }

        return _record;
    }

    @Transactional(readOnly = true)
    public String getVal(String groupId, String name, Boolean isEnabled) {
        return stream(Dictionary.class)
                .where(i -> groupId == null || groupId.equals(i.getGroupId()))
                .where(i -> name == null || name.equals(i.getName()))
                .where(i -> isEnabled == null || isEnabled == i.getIsEnabled())
                .select(Dictionary::getVal)
                .findFirst().orElse(null);
    }

    @Transactional(readOnly = true)
    public List<Dictionary> search(String groupId, String name, Boolean isEnabled, List<Sorter> sorters) {
        return search(-1, -1, sorters, groupId, name, isEnabled).records;
    }

    @Transactional(readOnly = true)
    public PaginatedRecords<Dictionary> search(int count, int index, List<Sorter> sorters, String groupId, String name, Boolean isEnabled) {
        JinqStream<Dictionary> _query = stream(Dictionary.class);

        if (StringUtils.hasLength(groupId)) {
            _query = _query.where(i -> i.getGroupId() != null && i.getGroupId().contains(groupId));
        }

        if (StringUtils.hasLength(name)) {
            _query = _query.where(i -> i.getName() != null && i.getName().contains(name));
        }

        if (isEnabled != null) {
            _query = _query.where(i -> isEnabled == i.getIsEnabled());
        }

        if (CollectionUtils.isEmpty(sorters)) {
            _query = _query.sortedBy(Dictionary::getSeq)
                    .sortedBy(Dictionary::getName)
                    .sortedBy(Dictionary::getGroupId);
        } else {
            _query = sort(_query, sorters);
        }

        PaginatedRecords<Dictionary> _page = new PaginatedRecords<>(count, index);
        _page.total = _query.count();

        if (count >= 0 && index >= 0) {
            _query = _query.skip((long) count * index).limit(count);
        }
        _page.records = _query.toList();

        return _page;
    }

    public Result<String> save(Dictionary record, String operatorId) {
        Result<String> _id = new Result<>();

        boolean _alreadyExisted = true;

        Result<Dictionary> _record = ((DictionaryRepository) proxy).get(record.getId(), null);
        if (!_record.isOK()) {
            _record.data = new Dictionary();
            _record.data.setBuildIn(false);
            _record.data.setReadonly(false);
            _alreadyExisted = false;
        }

        String _identifier = _record.data.getId();
        JinqStream<Dictionary> _query = stream(Dictionary.class).where(i -> _identifier == null || !_identifier.equals(i.getId()));

        String _groupId = record.getGroupId();
        String _name = record.getName();
        String _val = record.getVal();
        if (_query.where(i -> _groupId == null || _groupId.equals(i.getGroupId()))
                .where(i -> _name == null || _name.equals(i.getName()))
                .where(i -> _val == null || _val.equals(i.getVal()))
                .findFirst().isPresent()) {
            _id.setCode(Result.DATABASE_RECORD_ALREADY_EXIST);
            return _id;
        }

        if (_record.data.getReadonly()) {
            _record.data.setIsEnabled(record.getIsEnabled());
        } else {
            ((DictionaryRepository) proxy).copyProperties(record, _record.data, new String[]{"id", "buildIn", "readonly"});
        }

        Result<Void> _success = _alreadyExisted ? update(_record.data, operatorId) : add(_record.data, operatorId);
        if (!_success.isOK()) {
            return _id.pack(_success);
        }

        _id.data = _record.data.getId();
        return _id;
    }

    public Result<Void> remove(String id) {
        Result<Void> _success = new Result<>();

        Result<Dictionary> _record = ((DictionaryRepository) proxy).get(id, null);
        if (!_record.isOK()) {
            return _success.pack(_record);
        }

        // 内置项不支持删除
        if (_record.data.getBuildIn()) {
            _success.setCode(Result.DATA_ACCESS_DENY);
            return _success;
        }

        getCurrentSession().remove(_record.data);

        return _success;
    }

}