package com.chinamobile.sparrow.domain.repository.sys;

import com.chinamobile.sparrow.domain.infra.job.JobDetailAndTrigger;
import org.quartz.DisallowConcurrentExecution;
import org.quartz.JobExecutionContext;
import org.quartz.PersistJobDataAfterExecution;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.quartz.QuartzJobBean;

@DisallowConcurrentExecution
@PersistJobDataAfterExecution
@JobDetailAndTrigger(jobName = "unlock-login-excess", jobGroup = "default", triggerName = "unlock-login-excess", triggerGroup = "default", triggerCron = "0 0 0 * * ?", triggerOnStart = false)
public class UnlockAccountJob extends QuartzJobBean {

    protected final UserRepository<?> userRepository;
    protected final Logger logger;

    public UnlockAccountJob(UserRepository<?> userRepository) {
        this.userRepository = userRepository;
        this.logger = LoggerFactory.getLogger(this.getClass());
    }

    @Override
    protected void executeInternal(JobExecutionContext jobExecutionContext) {
        logger.info("作业开始");

        try {
            int _count = userRepository.unlockLoginExcess();
            logger.info("已解锁{}个账号", _count);
        } catch (Throwable e) {
            logger.error("作业执行时程序异常", e);
        } finally {
            logger.info("作业结束");
        }
    }

}