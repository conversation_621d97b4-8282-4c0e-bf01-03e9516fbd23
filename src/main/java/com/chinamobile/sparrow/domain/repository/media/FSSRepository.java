package com.chinamobile.sparrow.domain.repository.media;

import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.model.media.Media;
import com.chinamobile.sparrow.domain.service.FileSystemService;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.jinq.jpa.JinqJPAStreamProvider;
import org.springframework.util.Base64Utils;
import org.springframework.util.FileCopyUtils;
import org.springframework.util.StringUtils;

import javax.persistence.EntityManagerFactory;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public class FSSRepository extends AbstractMediaRepository {

    protected final String env;
    protected final String dir;
    protected final FileSystemService fileSystemService;

    public FSSRepository(EntityManagerFactory entityManagerFactory, JinqJPAStreamProvider jinqJPAStreamProvider, String extensionsAllow, String extensionsForbid, String env, String dir, FileSystemService fileSystemService) {
        super(entityManagerFactory, jinqJPAStreamProvider, extensionsAllow, extensionsForbid);
        this.env = env;
        this.dir = dir;
        this.fileSystemService = fileSystemService;
    }

    @Override
    public List<ImmutablePair<String, String>> getSubPaths(String path) {
        List<String> _temp = stream(Media.class).where(i -> i.getPath() != null && i.getPath().startsWith(path) && !i.getPath().equals(path))
                .select(Media::getPath)
                .distinct()
                .toList();

        _temp = _temp.stream()
                .map(i -> i.substring(StringUtils.hasLength(path) ? path.length() + 1 : 0).split(Pattern.quote(File.separator))[0])
                .distinct()
                .collect(Collectors.toList());

        List<ImmutablePair<String, String>> _paths = new ArrayList<>();
        for (String i : _temp) {
            _paths.add(ImmutablePair.of(StringUtils.hasLength(path) ? path + File.separator + i : i, i));
        }
        return _paths;
    }

    @Override
    public Result<Media> stage(String bucket, String fileName, String base64, String operatorId) {
        String _path = path(bucket);
        return stage(id -> upload(id, _path, fileName, base64), _path, fileName, operatorId);
    }

    @Override
    public Result<Media> stage(String bucket, File file, String operatorId) {
        String _path = path(bucket);
        return stage(id -> upload(id, _path, file.getName(), file), _path, file.getName(), operatorId);
    }

    @Override
    public Result<Media> add(String bucket, String fileName, String base64, boolean saveFile, String operatorId) {
        String _path = path(bucket);
        return add(id -> upload(id, _path, fileName, base64), _path, fileName, saveFile, false, operatorId);
    }

    @Override
    public Result<Media> add(String bucket, File file, boolean saveFile, String operatorId) {
        String _path = path(bucket);
        return add(id -> upload(id, _path, file.getName(), file), _path, file.getName(), saveFile, false, operatorId);
    }

    protected Result<String> upload(String id, String path, String fileName, String base64) {
        byte[] _bytes = Base64Utils.decodeFromString(base64);
        Result<Void> _success = xss(fileName, _bytes);
        return _success.isOK() ? fileSystemService.upload(id, path, fileName, _bytes) : new Result<String>().pack(_success);
    }

    protected Result<String> upload(String id, String path, String fileName, File file) {
        try {
            byte[] _bytes = FileCopyUtils.copyToByteArray(file);
            Result<Void> _success = xss(fileName, _bytes);
            return _success.isOK() ? fileSystemService.upload(id, path, file.getName(), _bytes) : new Result<String>().pack(_success);
        } catch (IOException e) {
            logger.error("保存文件失败", e);

            Result<String> _path = new Result<>();
            _path.setCode(Result.UNKNOWN);
            _path.message = e.getMessage();
            return _path;
        }
    }

    @Override
    public Result<Void> remove(String id, boolean force, String operatorId) {
        return remove((record) -> fileSystemService.remove(null, record.getContent()), id, force, operatorId);
    }

    @Override
    public Result<Void> remove(Media record, boolean force, String operatorId) {
        getCurrentSession().remove(record);

        return StringUtils.hasLength(record.getExtension()) ? fileSystemService.remove(null, record.getContent()) : new Result<>();
    }

    @Override
    public Result<InputStream> getInputStream(String id, String userId) {
        return ((FSSRepository) proxy).getInputStream(this::getInputStream, id, userId);
    }

    @Override
    public Result<InputStream> getInputStream(Media record) {
        return fileSystemService.download(null, record.getContent());
    }


    @Override
    public Result<byte[]> getBytes(String id, String userId) throws IOException {
        return getBytes(() -> ((FSSRepository) proxy).getInputStream(id, userId));
    }

    @Override
    public Result<byte[]> getBytes(Media record) throws IOException {
        return getBytes(() -> ((FSSRepository) proxy).getInputStream(record));
    }

    @Override
    public Result<String> getBase64String(String id, String userId) {
        return getBase64String(() -> {
            try {
                return ((FSSRepository) proxy).getBytes(id, userId);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        });
    }

    @Override
    public Result<String> getBase64String(Media record) {
        return getBase64String(() -> {
            try {
                return ((FSSRepository) proxy).getBytes(record);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        });
    }

    @Override
    public Result<byte[]> getPart(String id, Long start, Long end, String userId) {
        return getPart((record) -> ((FSSRepository) proxy).getPart(record, start, end), id, userId);
    }

    @Override
    public Result<byte[]> getPart(Media record, Long start, Long end) {
        return fileSystemService.downloadPart(null, record.getContent(), start, end);
    }

    @Override
    public Result<Long> getContentLength(String id, String userId) {
        return ((FSSRepository) proxy).getContentLength(this::getContentLength, id, userId);
    }

    @Override
    public Result<Long> getContentLength(Media record) {
        return fileSystemService.getContentLength(null, record.getContent());
    }

    @Override
    public String path(String bucket) {
        return Stream.of(env, StringUtils.hasLength(bucket) ? bucket : dir)
                .filter(StringUtils::hasLength)
                .collect(Collectors.joining("/"))
                .replace("/", File.separator);
    }

}