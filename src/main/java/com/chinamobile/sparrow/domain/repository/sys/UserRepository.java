package com.chinamobile.sparrow.domain.repository.sys;

import com.chinamobile.sparrow.domain.infra.code.ErrorCode;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.lang.PaginatedRecords;
import com.chinamobile.sparrow.domain.lang.Sorter;
import com.chinamobile.sparrow.domain.model.sys.Department;
import com.chinamobile.sparrow.domain.model.sys.User;
import com.chinamobile.sparrow.domain.repository.AbstractMediaAvailableRepository;
import com.chinamobile.sparrow.domain.repository.media.AbstractMediaRepository;
import com.chinamobile.sparrow.domain.util.CryptoUtil;
import org.apache.shiro.authc.IncorrectCredentialsException;
import org.jinq.jpa.JinqJPAStreamProvider;
import org.jinq.orm.stream.JinqStream;
import org.jinq.tuples.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.persistence.EntityManagerFactory;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.function.BiFunction;
import java.util.function.Function;
import java.util.stream.Collectors;

@ErrorCode(module = "002")
public class UserRepository<T extends User> extends AbstractMediaAvailableRepository<T> {

    protected final String passwordConstraint;
    protected final String rsaPrivateKey;
    protected final Logger logger;

    public UserRepository(EntityManagerFactory entityManagerFactory, JinqJPAStreamProvider jinqJPAStreamProvider, AbstractMediaRepository mediaRepository, String passwordConstraint, String rsaPrivateKey, Class<T> tClass) {
        super(entityManagerFactory, jinqJPAStreamProvider, mediaRepository, tClass);
        this.passwordConstraint = passwordConstraint;
        this.rsaPrivateKey = rsaPrivateKey;
        this.logger = LoggerFactory.getLogger(this.getClass());
    }

    @Override
    protected <V extends Comparable<V>> JinqStream<T> sort(JinqStream<T> query, BiFunction<JinqStream<T>, JinqStream.CollectComparable<T, V>, JinqStream<T>> compare, String field) {
        switch (field) {
            case "account":
                return compare.apply(query, i -> (V) i.getAccount());
            case "alphabet":
                return compare.apply(query, i -> (V) i.getAlphabet());
            case "birthday":
                return compare.apply(query, i -> (V) i.getBirthday());
            case "createTime":
                return compare.apply(query, i -> (V) i.getCreateTime());
            case "mp":
                return compare.apply(query, i -> (V) i.getMp());
            case "name":
                return compare.apply(query, i -> (V) i.getName());
            case "seq":
                return compare.apply(query, i -> (V) i.getSeq());
            default:
                return query;
        }
    }

    @Override
    public T mask(T record) {
        try {
            T _record = tClass.newInstance();

            ((UserRepository<T>) proxy).copyProperties(record, _record, new String[]{"password", "mp", "email", "birthday", "address", "isLocked", "isOnline", "loginAttempts", "yzyId"});

            return _record;
        } catch (InstantiationException | IllegalAccessException e) {
            logger.error("脱敏失败", e);
        }

        return null;
    }

    @Override
    public List<String> getMediaIds(List<T> records) {
        return CollectionUtils.isEmpty(records) ? new ArrayList<>() : records.stream()
                .map(User::getAvatarId)
                .collect(Collectors.toList());
    }

    @Transactional(readOnly = true)
    public Result<T> getBriefByIdOrAccountOrMp(String key, Boolean isEnabled) {
        return getBriefBy(query -> query.where(i -> key.equals(i.getId()) || key.equals(i.getAccount()) || key.equals(i.getMp())), isEnabled);
    }

    @Transactional(readOnly = true)
    public Result<T> getBriefByAccount(String account, Boolean isEnabled) {
        return getBriefBy(query -> query.where(i -> account.equals(i.getAccount())), isEnabled);
    }

    @Transactional(readOnly = true)
    public Result<T> getBriefByMp(String mp, Boolean isEnabled) {
        return getBriefBy(query -> query.where(i -> mp.equals(i.getMp())), isEnabled);
    }

    @Transactional(readOnly = true)
    public Result<T> getBriefByYzyId(String yzyId, Boolean isEnabled) {
        return getBriefBy(query -> query.where(i -> yzyId.equals(i.getYzyId())), isEnabled);
    }

    @Transactional(readOnly = true)
    public Result<T> getBriefBy(Function<JinqStream<T>, JinqStream<T>> func, Boolean isEnabled) {
        Result<T> _record = new Result<>();

        JinqStream<T> _query = stream(tClass);
        _query = func.apply(_query);

        _record.data = _query.where(i -> isEnabled == null || isEnabled == i.getIsEnabled())
                .findFirst().orElse(null);
        if (_record.data == null) {
            _record.setCode(Result.DATABASE_RECORD_NOT_FOUND, new Object[]{User.class.getSimpleName()});
        }

        return _record;
    }

    @Transactional(readOnly = true)
    public Result<T> getByIdOrAccountOrMp(String key, Boolean isEnabled) {
        return getBy(query -> query.where(i -> key.equals(i.getOne().getId()) || key.equals(i.getOne().getAccount()) || key.equals(i.getOne().getMp())), isEnabled);
    }

    @Transactional(readOnly = true)
    public Result<T> getByAccount(String account, Boolean isEnabled) {
        return getBy(query -> query.where(i -> account.equals(i.getOne().getAccount())), isEnabled);
    }

    @Transactional(readOnly = true)
    public Result<T> getByMp(String mp, Boolean isEnabled) {
        return getBy(query -> query.where(i -> mp.equals(i.getOne().getMp())), isEnabled);
    }

    @Transactional(readOnly = true)
    public Result<T> getByYzyId(String yzyId, Boolean isEnabled) {
        return getBy(query -> query.where(i -> yzyId.equals(i.getOne().getYzyId())), isEnabled);
    }

    @Transactional(readOnly = true)
    public Result<T> getBy(Function<JinqStream<Pair<T, Department>>, JinqStream<Pair<T, Department>>> func, Boolean isEnabled) {
        Result<T> _record = new Result<>();

        JinqStream<Pair<T, Department>> _query = stream(tClass)
                .leftOuterJoin((i, session) -> session.stream(Department.class), (i, dept) -> dept.getId().equals(i.getDeptId()));
        _query = func.apply(_query);

        Pair<T, Department> _relation = _query.where(i -> isEnabled == null || isEnabled == i.getOne().getIsEnabled())
                .findFirst().orElse(null);
        if (_relation == null) {
            _record.setCode(Result.DATABASE_RECORD_NOT_FOUND, new Object[]{User.class.getSimpleName()});
            return _record;
        }

        _record.data = _relation.getOne();
        if (_relation.getTwo() != null) {
            _record.data.setDeptName(_relation.getTwo().getName());
            _record.data.setDeptCode(_relation.getTwo().getCode());
            _record.data.setDeptFullName(_relation.getTwo().getFullName());
        }

        return _record;
    }

    @Transactional(readOnly = true)
    public List<T> suggest(int count, String rootDeptId, String key) {
        JinqStream<T> _query = stream(tClass).where(User::getIsEnabled);

        if (StringUtils.hasLength(rootDeptId)) {
            String _patten1 = rootDeptId + Department.CODE_SEPARATOR, _patten2 = Department.CODE_SEPARATOR + _patten1;
            List<String> _deptIds = stream(Department.class)
                    .where(i -> rootDeptId.equals(i.getId()) || i.getCode().startsWith(_patten1) || i.getCode().contains(_patten2))
                    .select(Department::getId)
                    .toList();

            _query = _deptIds.isEmpty() ? _query : _query.where(i -> _deptIds.contains(i.getDeptId()));
        }

        if (StringUtils.hasLength(key)) {
            _query = _query.where(i -> i.getName().contains(key) || i.getAccount().contains(key) || (i.getAlphabet() != null && i.getAlphabet().contains(key)) || (i.getMp() != null && i.getMp().contains(key)));
        }

        _query = _query.sortedBy(User::getName);

        if (count >= 0) {
            _query = _query.limit(count);
        }

        return _query.toList().stream()
                .map(this::eraseLogin)
                .collect(Collectors.toList());
    }

    @Transactional(readOnly = true)
    public List<T> search(String account, String deptId, String name, String mp, Boolean isEnabled, List<Sorter> sorters) {
        return search(-1, -1, sorters, account, deptId, name, mp, isEnabled).records;
    }

    @Transactional(readOnly = true)
    public PaginatedRecords<T> search(int count, int index, List<Sorter> sorters, String account, String deptId, String name, String mp, Boolean isEnabled) {
        JinqStream<T> _query = stream(tClass);

        if (StringUtils.hasLength(account)) {
            _query = _query.where(i -> i.getAccount().contains(account));
        }

        if (StringUtils.hasLength(deptId)) {
            _query = _query.where(i -> deptId.equals(i.getDeptId()));
        }

        if (StringUtils.hasLength(name)) {
            _query = _query.where(i -> name.equals(i.getName()));
        }

        if (StringUtils.hasLength(mp)) {
            _query = _query.where(i -> mp.equals(i.getMp()));
        }

        if (isEnabled != null) {
            _query = _query.where(i -> isEnabled == i.getIsEnabled());
        }

        if (CollectionUtils.isEmpty(sorters)) {
            _query = _query.sortedBy(User::getName)
                    .sortedBy(User::getSeq);
        } else {
            _query = sort(_query, sorters);
        }

        PaginatedRecords<T> _page = new PaginatedRecords<>(count, index);
        _page.total = _query.count();

        if (count >= 0 && index >= 0) {
            _query = _query.skip((long) count * index).limit(count);
        }
        _page.records = _query.toList().stream()
                .map(this::eraseLogin)
                .collect(Collectors.toList());

        return _page;
    }

    @Transactional(readOnly = true)
    public List<T> findBrief(List<String> ids, List<String> deptIds, List<String> accounts, List<String> names, List<String> mps, Boolean isEnabled, List<Sorter> sorters) {
        return findBrief(-1, -1, sorters, ids, deptIds, accounts, names, mps, isEnabled).records;
    }

    @Transactional(readOnly = true)
    public PaginatedRecords<T> findBrief(int count, int index, List<Sorter> sorters, List<String> ids, List<String> deptIds, List<String> accounts, List<String> names, List<String> mps, Boolean isEnabled) {
        JinqStream<T> _query = stream(tClass);

        if (ids != null) {
            _query = _query.where(i -> ids.contains(i.getId()));
        }

        if (deptIds != null) {
            _query = _query.where(i -> deptIds.contains(i.getDeptId()));
        }

        if (accounts != null) {
            _query = _query.where(i -> accounts.contains(i.getAccount()));
        }

        if (names != null) {
            _query = _query.where(i -> names.contains(i.getName()));
        }

        if (mps != null) {
            _query = _query.where(i -> mps.contains(i.getMp()));
        }

        if (isEnabled != null) {
            _query = _query.where(i -> isEnabled == i.getIsEnabled());
        }

        if (CollectionUtils.isEmpty(sorters)) {
            _query = _query.sortedBy(User::getName);
        } else {
            _query = sort(_query, sorters);
        }

        PaginatedRecords<T> _page = new PaginatedRecords<>(count, index);
        _page.total = _query.count();

        if (count >= 0 && index >= 0) {
            _query = _query.skip((long) count * index).limit(count);
        }
        _page.records = _query.toList().stream()
                .map(this::eraseLogin)
                .collect(Collectors.toList());

        return _page;
    }

    @Transactional(readOnly = true)
    public List<T> find(List<String> ids, List<String> deptIds, List<String> accounts, List<String> names, List<String> mps, Boolean isEnabled, List<Sorter> sorters) {
        return find(-1, -1, sorters, ids, deptIds, accounts, names, mps, isEnabled).records;
    }

    @Transactional(readOnly = true)
    public PaginatedRecords<T> find(int count, int index, List<Sorter> sorters, List<String> ids, List<String> deptIds, List<String> accounts, List<String> names, List<String> mps, Boolean isEnabled) {
        JinqStream<Pair<T, Department>> _query = stream(tClass)
                .leftOuterJoin((i, session) -> session.stream(Department.class), (i, dept) -> dept.getId().equals(i.getDeptId()));

        if (ids != null) {
            _query = _query.where(i -> ids.contains(i.getOne().getId()));
        }

        if (deptIds != null) {
            _query = _query.where(i -> deptIds.contains(i.getOne().getDeptId()));
        }

        if (accounts != null) {
            _query = _query.where(i -> accounts.contains(i.getOne().getAccount()));
        }

        if (names != null) {
            _query = _query.where(i -> names.contains(i.getOne().getName()));
        }

        if (mps != null) {
            _query = _query.where(i -> mps.contains(i.getOne().getMp()));
        }

        if (isEnabled != null) {
            _query = _query.where(i -> isEnabled == i.getOne().getIsEnabled());
        }

        if (CollectionUtils.isEmpty(sorters)) {
            _query = _query.sortedBy(i -> i.getOne().getName());
        } else {
            Collections.reverse(sorters);

            for (Sorter i : sorters) {
                switch (i.getField()) {
                    case "account":
                        _query = i.getAsc() ? _query.sortedBy(j -> j.getOne().getAccount()) : _query.sortedDescendingBy(j -> j.getOne().getAccount());
                        break;
                    case "name":
                        _query = i.getAsc() ? _query.sortedBy(j -> j.getOne().getName()) : _query.sortedDescendingBy(j -> j.getOne().getName());
                        break;
                    case "alphabet":
                        _query = i.getAsc() ? _query.sortedBy(j -> j.getOne().getAlphabet()) : _query.sortedDescendingBy(j -> j.getOne().getAlphabet());
                        break;
                    case "birthday":
                        _query = i.getAsc() ? _query.sortedBy(j -> j.getOne().getBirthday()) : _query.sortedDescendingBy(j -> j.getOne().getBirthday());
                        break;
                    case "seq":
                        _query = i.getAsc() ? _query.sortedBy(j -> j.getOne().getSeq()) : _query.sortedDescendingBy(j -> j.getOne().getSeq());
                        break;
                    case "createTime":
                        _query = i.getAsc() ? _query.sortedBy(j -> j.getOne().getCreateTime()) : _query.sortedDescendingBy(j -> j.getOne().getCreateTime());
                        break;
                }
            }
        }

        PaginatedRecords<T> _page = new PaginatedRecords<>(count, index);
        _page.total = _query.count();

        if (count >= 0 && index >= 0) {
            _query = _query.skip((long) count * index).limit(count);
        }

        _page.records = new ArrayList<>();
        for (Pair<T, Department> i : _query.toList()) {
            T _record = eraseLogin(i.getOne());

            if (i.getTwo() != null) {
                _record.setDeptName(i.getTwo().getName());
                _record.setDeptCode(i.getTwo().getCode());
                _record.setDeptFullName(i.getTwo().getFullName());
            }

            _page.records.add(_record);
        }

        return _page;
    }

    public Result<String> save(T record, String operatorId) throws InstantiationException, IllegalAccessException {
        Result<String> _id = new Result<>();

        boolean _alreadyExisted = true;

        Result<T> _record = ((UserRepository<T>) proxy).getBriefByIdOrAccountOrMp(record.getId(), null);
        if (!_record.isOK()) {
            _record.data = tClass.newInstance();

            _alreadyExisted = false;
        }

        String _identifier = _record.data.getId();
        JinqStream<T> _query = stream(tClass).where(i -> _identifier == null || !_identifier.equals(i.getId()));

        String _account = record.getAccount();
        String _mp = record.getMp();
        if (_query.where(i -> _account.equals(i.getAccount()) || (_mp != null && _mp.equals(i.getMp())))
                .findFirst().isPresent()) {
            _id.setCode(Result.DATABASE_RECORD_ALREADY_EXIST);
            return _id;
        }

        if (StringUtils.hasLength(record.getDeptId())) {
            _record.data.setDeptId(record.getDeptId());
        }

        _record.data.setMp(StringUtils.hasLength(_mp) ? _mp : null);

        ((UserRepository<T>) proxy).copyProperties(record, _record.data, new String[]{"id", "password", "isEnabled", "deptId", "mp", "yzyId", "isLocked", "isOnline", "loginAttempts"});

        Result<Void> _success = _alreadyExisted ? update(_record.data, operatorId) : add(_record.data, operatorId);
        if (!_success.isOK()) {
            return _id.pack(_success);
        }

        _id.data = _record.data.getId();
        return _id;
    }

    public Result<Void> enable(String id, boolean enabled, String operatorId) {
        Result<Void> _success = new Result<>();

        Result<T> _record = ((UserRepository<T>) proxy).getBriefByIdOrAccountOrMp(id, !enabled);
        if (!_record.isOK()) {
            return _success.pack(_record);
        }

        // 恢复账号需重置密码
        if (enabled) {
            _record.data.setPassword(null);
        }

        _record.data.setIsEnabled(enabled);
        return update(_record.data, operatorId);
    }

    public Result<Void> lock(String id, boolean locked, String operatorId) {
        Result<Void> _success = new Result<>();

        Result<T> _record = ((UserRepository<T>) proxy).getBriefByIdOrAccountOrMp(id, null);
        if (!_record.isOK()) {
            return _success.pack(_record);
        }

        _record.data.setIsLocked(locked);
        return update(_record.data, operatorId);
    }

    public int unlockLoginExcess() {
        return getCurrentSession().createQuery("update User set loginAttempts = null where loginAttempts != null and isEnabled = true").executeUpdate();
    }

    public Result<Void> changePassword(String id, String originalPassword, String newPassword, String operatorId) {
        Result<Void> _success = new Result<>();

        Result<T> _record = ((UserRepository<T>) proxy).getBriefByIdOrAccountOrMp(id, null);
        if (!_record.isOK()) {
            return _success.pack(_record);
        }

        String _originalPassword;
        try {
            _originalPassword = CryptoUtil.decryptRsa(originalPassword, rsaPrivateKey);
        } catch (Throwable e) {
            _success.setCode(Result.ENUM_ERROR.P, 2);
            return _success;
        }

        String _password;
        try {
            _password = CryptoUtil.decryptRsa(_record.data.getPassword(), rsaPrivateKey);
        } catch (Throwable e) {
            throw new IncorrectCredentialsException(e);
        }

        if (!Objects.equals(_originalPassword, _password)) {
            _success.setCode(Result.ENUM_ERROR.P, 2);
            return _success;
        }

        _success = validatePassword(newPassword, _originalPassword);
        if (!_success.isOK()) {
            return _success;
        }

        _record.data.setPassword(newPassword);
        return update(_record.data, operatorId);
    }

    public Result<Void> resetPassword(String id, String password, String operatorId) {
        Result<Void> _success = new Result<>();

        Result<T> _record = ((UserRepository<T>) proxy).getBriefByIdOrAccountOrMp(id, null);
        if (!_record.isOK()) {
            return _success.pack(_record);
        }

        _success = validatePassword(password, null);
        if (!_success.isOK()) {
            return _success;
        }

        _record.data.setPassword(password);
        return update(_record.data, operatorId);
    }

    /**
     * 判断账号是否启用
     *
     * @param id
     * @return
     */
    @Transactional(readOnly = true)
    public boolean isEnabled(String id) {
        return stream(tClass).where(i -> i.getId().equals(id) && i.getIsEnabled())
                .findFirst().isPresent();
    }

    /**
     * 判断账号是否锁定
     *
     * @param id
     * @return
     */
    @Transactional(readOnly = true)
    public boolean isLocked(String id) {
        return stream(tClass).where(i -> i.getId().equals(id) && i.getIsLocked())
                .findFirst().isPresent();
    }

    @Transactional(readOnly = true)
    public PaginatedRecords<T> searchOnline(int count, int index, List<Sorter> sorters, String account, String deptId, String name, String mp) {
        JinqStream<T> _query = stream(tClass).where(User::getIsOnline);

        if (StringUtils.hasLength(account)) {
            _query = _query.where(i -> i.getAccount().contains(account));
        }

        if (StringUtils.hasLength(deptId)) {
            _query = _query.where(i -> deptId.equals(i.getDeptId()));
        }

        if (StringUtils.hasLength(name)) {
            _query = _query.where(i -> name.equals(i.getName()));
        }

        if (StringUtils.hasLength(mp)) {
            _query = _query.where(i -> mp.equals(i.getMp()));
        }

        if (CollectionUtils.isEmpty(sorters)) {
            _query = _query.sortedBy(User::getName);
        } else {
            _query = sort(_query, sorters);
        }

        PaginatedRecords<T> _page = new PaginatedRecords<>(count, index);
        _page.total = _query.count();

        if (count >= 0 && index >= 0) {
            _query = _query.skip((long) count * index).limit(count);
        }
        _page.records = _query.toList().stream()
                .map(this::mask)
                .collect(Collectors.toList());

        return _page;
    }

    public void login(String id) {
        Result<T> _record = ((UserRepository<T>) proxy).getBriefByIdOrAccountOrMp(id, true);
        if (!_record.isOK()) {
            return;
        }

        _record.data.setIsOnline(true);
        _record.data.setLoginAttempts(null);
        update(_record.data, _record.data.getId());
    }

    public void logout(String id) {
        Result<T> _record = ((UserRepository<T>) proxy).getBriefByIdOrAccountOrMp(id, true);
        if (!_record.isOK()) {
            return;
        }

        _record.data.setIsOnline(false);
        update(_record.data, _record.data.getId());
    }

    @Transactional(readOnly = true)
    public long registeredUserNum() {
        return stream(tClass).where(User::getIsEnabled).count();
    }

    @Transactional(readOnly = true)
    public long onlineUserNum() {
        return stream(tClass).where(i -> i.getIsOnline() && i.getIsEnabled()).count();
    }

    protected T eraseLogin(T record) {
        try {
            T _record = tClass.newInstance();

            BeanUtils.copyProperties(record, _record, "password", "isLocked", "isOnline", "loginAttempts");

            return _record;
        } catch (InstantiationException | IllegalAccessException e) {
            logger.error("脱敏失败", e);
        }

        return null;
    }

    Result<Void> validatePassword(String password, String originPassword) {
        Result<Void> _success = new Result<>();

        String _password;
        try {
            _password = CryptoUtil.decryptRsa(password, rsaPrivateKey);
        } catch (Exception e) {
            _success.setCode(Result.ENUM_ERROR.P, 4, new Object[]{e.getMessage()});
            return _success;
        }

        if (!StringUtils.hasLength(_password)) {
            _success.setCode(Result.ENUM_ERROR.P, 3);
            return _success;
        }

        if (originPassword != null && originPassword.equals(_password)) {
            _success.setCode(Result.ENUM_ERROR.P, 1);
            return _success;
        }

        if (!_password.matches(passwordConstraint)) {
            _success.setCode(Result.ENUM_ERROR.P, 3);
            return _success;
        }

        return _success;
    }

}