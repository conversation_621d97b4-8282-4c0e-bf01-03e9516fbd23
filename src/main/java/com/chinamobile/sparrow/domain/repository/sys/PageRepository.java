package com.chinamobile.sparrow.domain.repository.sys;

import com.chinamobile.sparrow.domain.model.sec.Permission;
import com.chinamobile.sparrow.domain.model.sys.Page;
import com.chinamobile.sparrow.domain.repository.AbstractEntityRepository;
import com.chinamobile.sparrow.domain.repository.sec.PermissionRepository;
import org.apache.shiro.SecurityUtils;
import org.jinq.jpa.JinqJPAStreamProvider;
import org.jinq.orm.stream.JinqStream;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.persistence.EntityManagerFactory;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class PageRepository extends AbstractEntityRepository<Page> {

    protected final PermissionRepository permissionRepository;

    public PageRepository(EntityManagerFactory entityManagerFactory, JinqJPAStreamProvider jinqJPAStreamProvider, PermissionRepository permissionRepository) {
        super(entityManagerFactory, jinqJPAStreamProvider, Page.class);
        this.permissionRepository = permissionRepository;
    }

    @Transactional(readOnly = true)
    public List<Page> find() {
        List<Permission> _permissions = permissionRepository.find(Permission.ENUM_TYPE.PAGE);

        // 过滤权限
        String[] _names = _permissions.stream()
                .map(Permission::getName)
                .toArray(String[]::new);
        boolean[] _permitted = SecurityUtils.getSubject().isPermitted(_names);

        // 设置资源
        List<String> _resources = new ArrayList<>();
        for (int i = 0; i < _permitted.length; i++) {
            if (_permitted[i]) {
                _resources.add(_permissions.get(i).getResource());
            }
        }

        JinqStream<Page> _query = stream(Page.class);
        if (CollectionUtils.isEmpty(_resources)) {
            _query = SecurityUtils.getSubject().isAuthenticated() || SecurityUtils.getSubject().isRemembered() ? _query.where(i -> !i.getRequireAuthorized()) : _query.where(i -> !i.getRequireAuthenticated());
        } else {
            _query = _query.where(i -> !i.getRequireAuthorized() || _resources.contains(i.getUrl()));
        }

        return _query.toList();
    }

    public void renew() {
        // 更新页面
        renewPages();
        getCurrentSession().flush();

        // 新增自定义菜单
        List<Page> _records = stream(Page.class).toList();
        customize(_records);
        getCurrentSession().flush();

        // 更新内置菜单
        _records = stream(Page.class).toList();
        updateBuildIn(_records);
    }

    public void renewPages() {
        // 读取页面
        List<Page> _records = stream(Page.class)
                .where(i -> i.getUrl() != null && !"".equals(i.getUrl()))
                .collect(Collectors.toList());

        // 读取存量路径
        List<String> _urls = _records.stream()
                .map(Page::getUrl)
                .collect(Collectors.toList());

        // 读取有效路径
        for (Map.Entry<String, String[]> i : readPagePermissionMappings().entrySet()) {
            if (_urls.contains(i.getKey())) {
                continue;
            }

            // 新增菜单
            Page _record = new Page();
            _record.setUrl(i.getKey());
            _record.setRequireAuthenticated(true);
            _record.setRequireAuthorized(i.getValue() != null && i.getValue().length > 0);

            add(_record, null);
        }

        // 删除菜单
        /*for (Menu i : _menus.stream()
                .filter(i -> !_paths.contains(i.getUrl()))
                .collect(Collectors.toList())) {
            getCurrentSession().remove(i);
        }*/
    }

    /**
     * 读取路径映射
     *
     * @return
     */
    public Map<String, String[]> readPagePermissionMappings() {
        return new HashMap<String, String[]>() {{
            put("/", null);
            put("/me/setting", null);
            put("/me/message", null);
            put("/media", new String[]{"media:record:index"});
            put("/sec/online", new String[]{"sec:online:index"});
            put("/sec/role", new String[]{"sec:role:index"});
            put("/sec/role/appointment", new String[]{"sec:role:user:index"});
            put("/sec/role/authorization", new String[]{"sec:role:permission:index"});
            put("/sec/role/edit", new String[]{"sec:role:editor"});
            put("/sys/contact", new String[]{"sys:contact:index"});
            put("/sys/department/edit", new String[]{"sys:department:editor"});
            put("/sys/dictionary", new String[]{"sys:dictionary:index"});
            put("/sys/dictionary/edit", new String[]{"sys:dictionary:editor"});
            put("/sys/job", new String[]{"sys:job:index"});
            put("/sys/log/error", new String[]{"sys:log:error"});
            put("/sys/log/info", new String[]{"sys:log:info"});
            put("/sys/sms/outbox", new String[]{"sys:sms:outbox:index"});
            put("/sys/stat", new String[]{"sys:stat:index"});
            put("/sys/user/edit", new String[]{"sys:user:editor"});
        }};
    }

    /**
     * 新增自定义菜单
     */
    protected void customize(List<Page> records) {
        Page _record = records.stream()
                .filter(i -> "我的".equals(i.getTitle()) && i.getLevel() == 1)
                .findFirst().orElse(null);
        String _id = _record == null ? add("我的", null, "UserOutlined", 1, 101, true) : _record.getId();

        if (records.stream().noneMatch(i -> "个人中心".equals(i.getTitle()) && _id.equals(i.getParentId()))) {
            add("个人中心", _id, "ProfileOutlined", "/me/setting", 2, 1, true, false);
        }

        if (records.stream().noneMatch(i -> "消息中心".equals(i.getTitle()) && _id.equals(i.getParentId()))) {
            add("消息中心", _id, "MessageOutlined", "/me/message", 2, 2, true, false);
        }

        if (records.stream().noneMatch(i -> "运营统计".equals(i.getTitle()) && i.getLevel() == 1)) {
            add("运营统计", null, "BarChartOutlined", 1, 102, true);
        }

        _record = records.stream()
                .filter(i -> "系统管理".equals(i.getTitle()) && i.getLevel() == 1)
                .findFirst().orElse(null);
        String _id2 = _record == null ? add("系统管理", null, "AppstoreOutlined", 1, 103, true) : _record.getId();

        if (records.stream().noneMatch(i -> "用户管理".equals(i.getTitle()) && _id2.equals(i.getParentId()))) {
            add("用户管理", _id2, "TeamOutlined", 2, 1, true);
        }

        if (records.stream().noneMatch(i -> "短信管理".equals(i.getTitle()) && _id2.equals(i.getParentId()))) {
            add("短信管理", _id2, "MailOutlined", 2, 5, true);
        }

        if (records.stream().noneMatch(i -> "日志管理".equals(i.getTitle()) && _id2.equals(i.getParentId()))) {
            add("日志管理", _id2, "CodeOutlined", 2, 7, true);
        }
    }

    /**
     * 修改内置菜单
     *
     * @param records
     */
    protected void updateBuildIn(List<Page> records) {
        update(records, "/", null, "主页", "HomeOutlined", 1, 1);

        update(records, "/sys/stat", "运营统计", "活跃统计", "DotChartOutlined", 2, 1);

        update(records, "/sec/role", "系统管理", "角色管理", "SecurityScanOutlined", 2, 2);

        update(records, "/sys/contact", "用户管理", "通讯录", "ContactsOutlined", 3, 1);
        update(records, "/sec/online", "用户管理", "在线管理", "DesktopOutlined", 3, 2);

        update(records, "/sys/job", "系统管理", "作业管理", "ScheduleOutlined", 2, 3);
        update(records, "/sys/dictionary", "系统管理", "字典管理", "ControlOutlined", 2, 4);

        update(records, "/sys/sms/outbox", "短信管理", "发件箱", "UploadOutlined", 3, 1);

        update(records, "/media", "系统管理", "文件管理", "FileOutlined", 2, 6);

        update(records, "/sys/log/info", "日志管理", "访问日志", "FileSearchOutlined", 3, 1);
        update(records, "/sys/log/error", "日志管理", "错误日志", "BugOutlined", 3, 2);

        update(records, "/sec/role/edit", null, "角色编辑", null, null, null);
        update(records, "/sec/role/authorization", null, "角色授权", null, null, null);
        update(records, "/sec/role/appointment", null, "角色任命", null, null, null);
        update(records, "/sys/user/edit", null, "用户编辑", null, null, null);
        update(records, "/sys/dictionary/edit", null, "字典项目编辑", null, null, null);
    }

    protected String add(String title, String parentId, String icon, Integer level, Integer seq, boolean requireAuthenticated) {
        Page _record = new Page(title, parentId, icon, level, seq, requireAuthenticated, false);

        add(_record, null);

        return _record.getId();
    }

    protected String add(String title, String parentId, String icon, String url, Integer level, Integer seq, boolean requireAuthenticated, boolean requireAuthorized) {
        Page _record = new Page(title, parentId, icon, url, level, seq, requireAuthenticated, requireAuthorized);

        add(_record, null);

        return _record.getId();
    }

    protected void update(List<Page> source, String url, String parentTitle, String title, String icon, Integer level, Integer seq) {
        update(source, url, parentTitle, title, icon, level, seq, null, null);
    }

    protected void update(List<Page> source, String url, String parentTitle, String title, String icon, Integer level, Integer seq, Boolean requireAuthenticated, Boolean requireAuthorized) {
        Page _record = source.stream()
                .filter(i -> url.equals(i.getUrl()))
                .findFirst().orElse(null);
        // 只自动更新一次，以支持数据维护
        if (_record != null && _record.getMaintainTime() == null) {
            if (StringUtils.hasLength(parentTitle)) {
                _record.setParentId(source.stream()
                        .filter(i -> parentTitle.equals(i.getTitle()))
                        .filter(i -> level == 1 || i.getLevel() == level - 1)
                        .map(Page::getId)
                        .findFirst().orElse(null));
            }
            _record.setTitle(title);
            _record.setIcon(icon);
            _record.setLevel(level);
            _record.setSeq(seq);

            if (requireAuthenticated != null) {
                _record.setRequireAuthenticated(requireAuthenticated);
            }

            if (requireAuthorized != null) {
                _record.setRequireAuthorized(requireAuthorized);
                _record.setRequireAuthenticated(true);
            }

            update(_record, null);
        }
    }

}