package com.chinamobile.sparrow.domain.repository.sec;

import com.chinamobile.sparrow.domain.infra.code.ErrorCode;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.model.sec.VerificationCode;
import com.chinamobile.sparrow.domain.repository.AbstractEntityRepository;
import com.chinamobile.sparrow.domain.repository.sys.sms.SentSmsRepository;
import com.chinamobile.sparrow.domain.util.DateUtil;
import com.chinamobile.sparrow.domain.util.ValidatorUtil;
import org.jinq.jpa.JinqJPAStreamProvider;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.EntityManagerFactory;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.Objects;

@ErrorCode(module = "004")
public class VerificationCodeRepository extends AbstractEntityRepository<VerificationCode> {

    protected final String expiresIn;
    protected final int codeLength;
    protected final String template;
    protected final String sign;
    protected final String symbols;
    protected final SentSmsRepository sentSmsRepository;

    public VerificationCodeRepository(EntityManagerFactory entityManagerFactory, JinqJPAStreamProvider jinqJPAStreamProvider, String expiresIn, int codeLength, String template, String sign, SentSmsRepository sentSmsRepository) {
        super(entityManagerFactory, jinqJPAStreamProvider, VerificationCode.class);
        this.expiresIn = expiresIn;
        this.codeLength = codeLength;
        this.template = template;
        this.sign = sign;
        this.symbols = "**********";
        this.sentSmsRepository = sentSmsRepository;
    }

    @Transactional(readOnly = true)
    public VerificationCode get(String clientId, String mp) {
        Date _date = new Date();
        return stream(VerificationCode.class).where(i -> clientId.equals(i.getClientId()) && mp.equals(i.getMp()) && !_date.after(i.getExp()))
                .sortedDescendingBy(VerificationCode::getExp)
                .findFirst().orElse(null);
    }

    public Result<Long> add(String clientId, String mp, String operatorId) {
        Result<Long> _seconds = new Result<>();

        if (!ValidatorUtil.isMp(mp)) {
            _seconds.setCode(Result.ENUM_ERROR.P, 1);
            return _seconds;
        }

        Date _date = new Date();

        VerificationCode _record = stream(VerificationCode.class).where(i -> clientId.equals(i.getClientId()) && mp.equals(i.getMp()) && !i.getExp().before(_date))
                .findFirst().orElse(null);
        if (_record != null) {
            _seconds.setCode(Result.ENUM_ERROR.P, 2, new Object[]{expiresIn});
            _seconds.data = DateUtil.diff(Calendar.SECOND, _date, _record.getExp());
            return _seconds;
        }

        _record = new VerificationCode();
        _record.setClientId(clientId);
        _record.setMp(mp);

        // 设置验证码
        char[] _chars = new char[codeLength];
        for (int i = 0; i < codeLength; i++) {
            _chars[i] = symbols.charAt((int) (Math.random() * symbols.length()));
        }
        _record.setCode(new String(_chars));

        Result<String> _smsId = sendSms(_record.getMp(), _record.getCode(), operatorId);
        if (!_smsId.isOK()) {
            return _seconds.pack(_smsId);
        }

        // 设置短信id
        _record.setSmsId(_smsId.data);

        // 设置失效时间
        Date _date2 = new Date();
        _record.setExp(DateUtil.addMinutes(_date2, Integer.parseInt(expiresIn)));

        Result<Void> _success = add(_record, operatorId);
        if (!_success.isOK()) {
            return _seconds.pack(_success);
        }

        _seconds.data = DateUtil.diff(Calendar.SECOND, _date2, _record.getExp());
        return _seconds;
    }

    @Transactional(readOnly = true)
    public boolean verify(String clientId, String mp, String code) {
        VerificationCode _record = get(clientId, mp);
        return _record != null && Objects.equals(code, _record.getCode());
    }

    protected Result<String> sendSms(String mp, String code, String operatorId) {
        return sentSmsRepository.inbox(Collections.singletonList(mp), template, Collections.singletonList(code), sign, operatorId);
    }

}