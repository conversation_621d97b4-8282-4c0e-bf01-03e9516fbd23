package com.chinamobile.sparrow.domain.repository;

import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.lang.ThreeParamsConsumer;
import com.chinamobile.sparrow.domain.util.POIUtil;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.jinq.jpa.JinqJPAStreamProvider;
import org.jinq.orm.stream.JinqStream;
import org.jinq.tuples.Tuple3;
import org.springframework.aop.framework.AopContext;
import org.springframework.util.Base64Utils;

import javax.persistence.EntityManagerFactory;
import java.io.File;
import java.util.*;
import java.util.stream.Collectors;

public abstract class AbstractJinqRepository {

    protected final EntityManagerFactory entityManagerFactory;
    protected final JinqJPAStreamProvider jinqJPAStreamProvider;
    protected final AbstractJinqRepository proxy;

    protected AbstractJinqRepository(EntityManagerFactory entityManagerFactory, JinqJPAStreamProvider jinqJPAStreamProvider) {
        this.entityManagerFactory = entityManagerFactory;
        this.jinqJPAStreamProvider = jinqJPAStreamProvider;

        AbstractJinqRepository _temp;
        try {
            _temp = (AbstractJinqRepository) AopContext.currentProxy();
        } catch (Throwable e) {
            _temp = this;
        }
        this.proxy = _temp;
    }

    protected Session openSession() {
        return entityManagerFactory.unwrap(SessionFactory.class).openSession();
    }

    protected Session getCurrentSession() {
        return entityManagerFactory.unwrap(SessionFactory.class).getCurrentSession();
    }

    protected <T> JinqStream<T> stream(final Class<T> type) {
        return jinqJPAStreamProvider.streamAll(getCurrentSession(), type);
    }

    protected <T> JinqStream<T> stream(Session session, final Class<T> type) {
        return jinqJPAStreamProvider.streamAll(session, type);
    }

    protected Result<String> readExcel(ThreeParamsConsumer<Map<String, Integer>, List<String[]>, List<Result<?>>> consume, File excel, Integer sheet, Integer headerRowIndex) throws Exception {
        List<Result<?>> _results = new ArrayList<>();

        List<Tuple3<Integer, Integer, Throwable>> _errors = new ArrayList<>();
        List<String[]> _lines = POIUtil.readExcel(cell -> {
            try {
                // 未赋值则返回
                if (cell == null) {
                    return null;
                }

                // 行已被标记为失败，则返回
                if (_errors.stream().anyMatch(i -> Objects.equals(i.getOne(), cell.getRowIndex()))) {
                    return null;
                }

                return POIUtil.getCellValueByType(cell);
            } catch (Throwable e) {
                _errors.add(new Tuple3<>(cell.getRowIndex(), cell.getColumnIndex(), e));
                return null;
            }
        }, excel, sheet == null ? 0 : sheet);

        headerRowIndex = headerRowIndex == null || headerRowIndex < 0 ? 0 : headerRowIndex;
        int _length = _lines.get(headerRowIndex).length;

        // 设置表头映射
        Map<String, Integer> _header = new LinkedHashMap<>();
        for (int i = 0; i < _length; i++) {
            if (_lines.get(headerRowIndex)[i] != null) {
                _header.put(_lines.get(headerRowIndex)[i], i);
            }
        }

        // 以表头确定有效列
        _lines = _lines.stream()
                .map(i -> Arrays.copyOf(i, _length))
                .collect(Collectors.toList());

        // 设置结果
        for (int i = headerRowIndex + 1; i < _lines.size(); i++) {
            Result<Void> _success = new Result<>();

            // 读取失败
            int _index = i;
            Tuple3<Integer, Integer, Throwable> _error = _errors.stream()
                    .filter(j -> _index == j.getOne())
                    .findFirst().orElse(null);
            if (_error != null) {
                _success.setCode(Result.ENUM_ERROR.P, 1, new Object[]{_error.getTwo() + 1, _error.getThree().getMessage()});
            }
            // 空白行
            else if (org.apache.commons.lang3.StringUtils.isAllBlank(_lines.get(i))) {
                _success.setCode(Result.ENUM_ERROR.P, 2);
            }

            _results.add(i - headerRowIndex - 1, _success);
        }

        List<String> _columns = new ArrayList<>();
        // 删除标题行
        for (int i = 0; i <= headerRowIndex; i++) {
            _columns.add(null);

            _lines.remove(0);
        }

        consume.accept(_header, _lines, _results);

        // 设置结果列
        for (Result<?> result : _results) {
            _columns.add(result.isOK() ? "保存成功" : result.message);
        }

        // 将结果输出到Excel
        byte[] _bytes = POIUtil.appendColumnToExcel(excel, sheet, _columns);

        // 编码为base64
        Result<String> _base64 = new Result<>();
        _base64.data = Base64Utils.encodeToString(_bytes);
        return _base64;
    }

}